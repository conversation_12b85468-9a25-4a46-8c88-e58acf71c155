<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cec-smartSurvey-backend</artifactId>
        <groupId>com.cec</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>smartSurvey-common-bom</module>
        <module>smartSurvey-common-social</module>
        <module>smartSurvey-common-core</module>
        <module>smartSurvey-common-doc</module>
        <module>smartSurvey-common-excel</module>
        <module>smartSurvey-common-idempotent</module>
        <!--<module>smartSurvey-common-job</module>-->
        <module>smartSurvey-common-log</module>
        <module>smartSurvey-common-mail</module>
        <module>smartSurvey-common-mybatis</module>
        <module>smartSurvey-common-oss</module>
        <module>smartSurvey-common-ratelimiter</module>
        <module>smartSurvey-common-redis</module>
        <module>smartSurvey-common-satoken</module>
        <module>smartSurvey-common-security</module>
       <!-- <module>smartSurvey-common-sms</module>-->
        <module>smartSurvey-common-web</module>
        <module>smartSurvey-common-translation</module>
        <module>smartSurvey-common-sensitive</module>
        <module>smartSurvey-common-json</module>
        <module>smartSurvey-common-encrypt</module>
        <module>smartSurvey-common-tenant</module>
        <!--<module>smartSurvey-common-websocket</module>-->
        <!--<module>smartSurvey-common-sse</module>-->
    </modules>

    <artifactId>smartSurvey-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
