package com.cec.common.redis.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cec.common.core.utils.SpringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 缓存操作工具类
 *
 * <AUTHOR>
 */
@Component
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CacheUtils {
    private static final CacheManager CACHE_MANAGER = SpringUtils.getBean(CacheManager.class);
    private final String ETERNAL_CACHE_NAME = "eternal_cache";

    /**
     * 获取缓存值
     *
     * @param cacheNames 缓存组名称
     * @param key        缓存key
     */
    public static <T> T get(String cacheNames, Object key) {
        Cache.ValueWrapper wrapper = CACHE_MANAGER.getCache(cacheNames).get(key);
        return wrapper != null ? (T) wrapper.get() : null;
    }

    /**
     * 保存缓存值
     *
     * @param cacheNames 缓存组名称
     * @param key        缓存key
     * @param value      缓存值
     */
    public static void put(String cacheNames, Object key, Object value) {
        CACHE_MANAGER.getCache(cacheNames).put(key, value);
    }

    /**
     * 删除缓存值
     *
     * @param cacheNames 缓存组名称
     * @param key        缓存key
     */
    public static void evict(String cacheNames, Object key) {
        CACHE_MANAGER.getCache(cacheNames).evict(key);
    }

    /**
     * 清空缓存值
     *
     * @param cacheNames 缓存组名称
     */
    public static void clear(String cacheNames) {
        CACHE_MANAGER.getCache(cacheNames).clear();
    }

    /**
     * 自增
     *
     * @param key
     * @param number
     * @return
     */
    public Long incr(String key, Integer number) {
        String v = get(key);
        if (StrUtil.isBlank(v)) {
            v = "0";
        }
        long finalValue = Convert.toLong(v) + number;
        save(key, String.valueOf(finalValue));
        return finalValue;
    }

    /**
     * 保存到Cache
     */
    public void save(String key, String value) {
        Objects.requireNonNull(CACHE_MANAGER.getCache(ETERNAL_CACHE_NAME)).put(key, value);
    }

    /**
     * 获取
     *
     * @param key
     */
    public String get(String key) {
        Cache.ValueWrapper valueWrapper = CACHE_MANAGER.getCache(ETERNAL_CACHE_NAME).get(key);
        if (ObjectUtil.isNotNull(valueWrapper) && ObjectUtil.isNotNull(valueWrapper.get())) {
            return Objects.requireNonNull(valueWrapper.get()).toString();
        }
        return null;
    }
}
