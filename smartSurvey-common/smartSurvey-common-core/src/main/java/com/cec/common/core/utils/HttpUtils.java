package com.cec.common.core.utils;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class HttpUtils {

    public static JSONObject get(String url, Map<String, Object> queryParams, Map<String, String> headers) {
        try (HttpResponse resp = HttpRequest.get(url)
            .form(queryParams)
            .addHeaders(headers)
            .execute()) {
            return new JSONObject(resp.body());
        } catch (Exception e) {
            log.error("发起Http请求异常", e);
        }
        return null;
    }

    public static JSONObject postForm(String url, Map<String, Object> queryParams, Map<String, String> headers) {
        try (HttpResponse resp = HttpRequest.post(url)
            .addHeaders(headers)
            .form(queryParams)//表单内容
            .timeout(20000)//超时，毫秒
            .execute()) {
            return new JSONObject(resp.body());
        } catch (Exception e) {
            log.error("发起Http请求异常", e);
        }
        return null;
    }

    public static String postJson(String url, Object object, Map<String, String> headers) {
        try (HttpResponse resp = HttpRequest.post(url)
            .body(JSONUtil.toJsonStr(object))
            .addHeaders(headers)
            .execute()) {
            return resp.body();
        } catch (Exception e) {
            log.error("发起Http请求异常", e);
        }
        return null;
    }

    /**
     * 获取请求IP地址
     *
     * @param request 请求
     * @return ip地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }

        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        //"***.***.***.***".length() = 15
        if (StrUtil.isNotBlank(ip) && ip.length() > 15) {
            if (ip.indexOf(CharUtil.COMMA) > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        //处理获取多个ip地址情况 nginx多层代理会出现多个ip 第一个为真实ip地址
        return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
    }

    /***
     * 通过request请求获取浏览器信息
     * @param request
     * @return
     */
    public static String getBrowser(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        return request.getHeader("User-Agent");
    }
}
