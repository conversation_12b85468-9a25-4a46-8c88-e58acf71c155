package com.cec.common.oss.factory;

import com.cec.common.core.constant.CacheNames;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.oss.constant.OssConstant;
import com.cec.common.oss.core.OssClient;
import com.cec.common.oss.exception.OssException;
import com.cec.common.oss.properties.OssConfigProperties.OssConfig;
import com.cec.common.oss.properties.OssProperties;
import com.cec.common.redis.utils.CacheUtils;
import com.cec.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 文件上传Factory
 *
 * <AUTHOR> Li
 */
@Slf4j
public class OssFactory {

    private static final Map<String, OssClient> CLIENT_CACHE = new ConcurrentHashMap<>();
    private static final ReentrantLock LOCK = new ReentrantLock();

    /**
     * 静态实例，用于桥接静态方法和注入式使用
     */
    private static OssFactory instance;

    private final OssProperties ossProperties;

    public OssFactory(OssProperties ossProperties) {
        this.ossProperties = ossProperties;
        instance = this;
    }

    /**
     * 获取默认实例 (静态方法)
     */
    public static OssClient instance() {
        if (instance != null) {
            return instance.instanceInternal();
        }

        // 如果未使用Spring自动配置，则尝试从SpringUtils获取环境变量
        Environment environment = SpringUtils.getBean(Environment.class);
        boolean isConfigEnabled = Boolean.TRUE.toString().equalsIgnoreCase(environment.getProperty("oss.enabled"));

        if (isConfigEnabled) {
            String configKey = environment.getProperty("oss.default-config");
            if (StringUtils.isEmpty(configKey)) {
                throw new OssException("文件存储服务配置错误，未找到默认配置key!");
            }
            return instanceFromConfigLegacy(configKey);
        }

        // 从Redis获取默认类型
        String configKey = RedisUtils.getCacheObject(OssConstant.DEFAULT_CONFIG_KEY);
        if (StringUtils.isEmpty(configKey)) {
            throw new OssException("文件存储服务类型无法找到!");
        }
        return instanceFromDbLegacy(configKey);
    }

    /**
     * 根据类型获取实例 (静态方法)
     */
    public static OssClient instance(String configKey) {
        if (instance != null) {
            return instance.instanceInternal(configKey);
        }

        // 如果未使用Spring自动配置，则回退到原有逻辑
        Environment environment = SpringUtils.getBean(Environment.class);
        boolean isConfigEnabled = Boolean.TRUE.toString().equalsIgnoreCase(environment.getProperty("oss.enabled"));

        if (isConfigEnabled) {
            return instanceFromConfigLegacy(configKey);
        }

        return instanceFromDbLegacy(configKey);
    }

    /**
     * 获取默认实例 (实例方法)
     */
    public OssClient instanceInternal() {
        // 尝试从配置文件获取
        if (isConfigEnabled()) {
            String configKey = ossProperties.getDefaultConfig();
            if (StringUtils.isEmpty(configKey)) {
                log.error("文件存储服务配置错误，未找到默认配置key!");
                throw new OssException("文件存储服务配置错误，未找到默认配置key!");
            }
            return instanceFromConfig(configKey);
        }

        // 从Redis获取默认类型
        String configKey = RedisUtils.getCacheObject(OssConstant.DEFAULT_CONFIG_KEY);
        if (StringUtils.isEmpty(configKey)) {
            log.error("文件存储服务类型无法找到!");
            throw new OssException("文件存储服务类型无法找到!");
        }
        return instanceFromDb(configKey);
    }

    /**
     * 根据类型获取实例 (实例方法)
     */
    public OssClient instanceInternal(String configKey) {
        // 尝试从配置文件获取
        if (isConfigEnabled()) {
            return instanceFromConfig(configKey);
        }

        return instanceFromDb(configKey);
    }

    /**
     * 检查配置是否启用
     */
    private boolean isConfigEnabled() {
        return Boolean.TRUE.equals(ossProperties.getEnabled());
    }

    /**
     * 从配置文件获取OSS实例 (原始静态方法，用于未启用自动配置时)
     */
    private static OssClient instanceFromConfigLegacy(String configKey) {
        Environment env = SpringUtils.getBean(Environment.class);
        String prefix = "oss.configs." + configKey + ".";

        OssProperties properties = new OssProperties();
        properties.setEndpoint(env.getProperty(prefix + "endpoint"));
        properties.setDomain(env.getProperty(prefix + "domain"));
        properties.setPrefix(env.getProperty(prefix + "prefix"));
        properties.setAccessKey(env.getProperty(prefix + "accessKey"));
        properties.setSecretKey(env.getProperty(prefix + "secretKey"));
        properties.setBucketName(env.getProperty(prefix + "bucketName"));
        properties.setRegion(env.getProperty(prefix + "region"));
        properties.setIsHttps(env.getProperty(prefix + "isHttps"));
        properties.setAccessPolicy(env.getProperty(prefix + "accessPolicy"));

        if (StringUtils.isEmpty(properties.getEndpoint())) {
            log.error("系统异常, '{}' 配置信息不存在于配置文件中!", configKey);
            throw new OssException("系统异常, '" + configKey + "'配置信息不存在于配置文件中!");
        }

        // 使用配置key作为缓存key
        String key = "config:" + configKey;
        OssClient client = CLIENT_CACHE.get(key);
        // 客户端不存在或配置不相同则重新构建
        if (client == null || !client.checkPropertiesSame(properties)) {
            LOCK.lock();
            try {
                client = CLIENT_CACHE.get(key);
                if (client == null || !client.checkPropertiesSame(properties)) {
                    CLIENT_CACHE.put(key, new OssClient(configKey, properties));
                    return CLIENT_CACHE.get(key);
                }
            } finally {
                LOCK.unlock();
            }
        }
        return client;
    }

    /**
     * 从配置文件获取OSS实例 (实例方法，使用自动配置)
     */
    private OssClient instanceFromConfig(String configKey) {

        if (ossProperties.getConfigs() == null) {
            log.error("OSS配置不存在，configs为null");
            throw new OssException("OSS配置不存在，请检查配置项");
        }

        Map<String, OssConfig> configMap = ossProperties.getConfigs();
        if (configMap == null || configMap.isEmpty()) {
            log.error("OSS配置映射为空，请检查配置项");
            throw new OssException("OSS配置不存在，请检查配置项");
        }

        OssConfig config = configMap.get(configKey);
        if (config == null) {
            log.error("系统异常, '{}' 配置信息不存在于配置文件中!", configKey);
            throw new OssException("系统异常, '" + configKey + "'配置信息不存在于配置文件中!");
        }

        OssProperties properties = new OssProperties();
        properties.setEndpoint(config.getEndpoint());
        properties.setDomain(config.getDomain());
        properties.setPrefix(config.getPrefix());
        properties.setAccessKey(config.getAccessKey());
        properties.setSecretKey(config.getSecretKey());
        properties.setBucketName(config.getBucketName());
        properties.setRegion(config.getRegion());
        properties.setIsHttps(config.getIsHttps());
        properties.setAccessPolicy(config.getAccessPolicy());

        // 使用配置key作为缓存key
        String key = "config:" + configKey;
        OssClient client = CLIENT_CACHE.get(key);
        // 客户端不存在或配置不相同则重新构建
        if (client == null || !client.checkPropertiesSame(properties)) {
            LOCK.lock();
            try {
                client = CLIENT_CACHE.get(key);
                if (client == null || !client.checkPropertiesSame(properties)) {
                    CLIENT_CACHE.put(key, new OssClient(configKey, properties));
                    log.info("创建OSS实例 key => {} (配置文件)", configKey);
                    return CLIENT_CACHE.get(key);
                }
            } finally {
                LOCK.unlock();
            }
        }
        return client;
    }

    /**
     * 从数据库获取OSS实例 (原始静态方法，用于未启用自动配置时)
     */
    private static OssClient instanceFromDbLegacy(String configKey) {
        return instanceFromDb(configKey);
    }

    /**
     * 从数据库获取OSS实例
     */
    private static OssClient instanceFromDb(String configKey) {
        String json = CacheUtils.get(CacheNames.SYS_OSS_CONFIG, configKey);
        if (json == null) {
            log.error("系统异常, '{}' 配置信息不存在于数据库!", configKey);
            throw new OssException("系统异常, '" + configKey + "'配置信息不存在于数据库!");
        }
        OssProperties properties = JsonUtils.parseObject(json, OssProperties.class);
        // 使用租户标识避免多个租户相同key实例覆盖
        String key = configKey;
        if (StringUtils.isNotBlank(properties.getTenantId())) {
            key = properties.getTenantId() + ":" + configKey;
        }
        OssClient client = CLIENT_CACHE.get(key);
        // 客户端不存在或配置不相同则重新构建
        if (client == null || !client.checkPropertiesSame(properties)) {
            LOCK.lock();
            try {
                client = CLIENT_CACHE.get(key);
                if (client == null || !client.checkPropertiesSame(properties)) {
                    CLIENT_CACHE.put(key, new OssClient(configKey, properties));
                    log.info("创建OSS实例 key => {} (数据库)", configKey);
                    return CLIENT_CACHE.get(key);
                }
            } finally {
                LOCK.unlock();
            }
        }
        return client;
    }
}
