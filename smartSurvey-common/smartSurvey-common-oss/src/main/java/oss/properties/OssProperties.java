package oss.properties;

import com.cec.common.oss.properties.OssConfigProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * OSS对象存储 配置属性
 *
 * <AUTHOR> Li
 */
@Data
@ConfigurationProperties(prefix = "oss")
public class OssProperties {

    /**
     * 是否启用配置文件配置
     */
    private Boolean enabled = false;

    /**
     * 默认配置Key
     */
    private String defaultConfig;

    /**
     * 配置项集合
     */
    private OssConfigProperties configs;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 访问站点
     */
    private String endpoint;

    /**
     * 自定义域名
     */
    private String domain;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * ACCESS_KEY
     */
    private String accessKey;

    /**
     * SECRET_KEY
     */
    private String secretKey;

    /**
     * 存储空间名
     */
    private String bucketName;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 是否https（Y=是,N=否）
     */
    private String isHttps;

    /**
     * 桶权限类型(0private 1public 2custom)
     */
    private String accessPolicy;

}
