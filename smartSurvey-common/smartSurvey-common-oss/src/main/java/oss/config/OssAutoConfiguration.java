package oss.config;

import com.cec.common.oss.core.OssClient;
import com.cec.common.oss.factory.OssFactory;
import com.cec.common.oss.properties.OssProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * OSS自动配置类
 */
@AutoConfiguration
@EnableConfigurationProperties(OssProperties.class)
public class OssAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public OssFactory ossFactory(OssProperties ossProperties) {
        return new OssFactory(ossProperties);
    }

    /**
     * 提供默认的OssClient Bean，方便直接注入使用
     * 只有在oss.enabled=true时才创建
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "oss", name = "enabled", havingValue = "true")
    public OssClient ossClient(OssFactory ossFactory) {
        return ossFactory.instance();
    }
}
