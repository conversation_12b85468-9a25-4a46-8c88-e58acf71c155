package com.cec.business.domain.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.domain.bo.UserFormLogicEntity;
import com.cec.business.domain.enums.FormJumpTypeEnum;
import com.cec.business.domain.enums.FormTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class UserFormDetailVO {
    /**
     * 表单基础信息
     */
    private UserForm form;

    /**
     * 表单项
     */
    private List<UserFormItemCopyVO> formItems;

    /**
     * 逻辑
     */
    private UserFormLogicEntity formLogic;


    @Data
    public static class UserForm {
        /**
         * 项目ID
         */
        private Long projectId;

        /***
         * 表单key
         */
        private String formKey;

        /**
         * 表单名称
         */
        private String name;

        /**
         * 表单描述
         */
        private String description;

        /***
         * 表单类型
         */
        private FormTypeEnum type;

        /**
         * 是否需要登录(1-是；0-否)
         */
        private Boolean needLogin;

        /**
         * 匿名是否可填写(1-是；0-否)
         */
        private Boolean anonymousEnabled;

        /**
         * 答题是否有有效期(1-是；0-否)
         */
        private Boolean validateEnabled;

        /**
         * 答题开始时间
         */
        private Date validateStartDate;

        /**
         * 答题结束时间
         */
        private Date validateEndDate;

        /**
         * 是否可预览答卷(1-是；0-否)
         */
        private Boolean previewEnabled;

        /**
         * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
         */
        private FormJumpTypeEnum jumpType;

        /**
         * 跳转内容
         */
        private String jumpContent;

        /**
         * 指定页面路径
         */
        private String jumpUrl;

        public UserForm(UserFormEntity entity) {
            this.projectId = entity.getProjectId();
            this.formKey = entity.getFormKey();
            this.name = entity.getName();
            this.description = entity.getDescription();
            this.type = entity.getType();
            this.needLogin = entity.getNeedLogin();
            this.anonymousEnabled = entity.getAnonymousEnabled();
            this.validateEnabled = entity.getValidateEnabled();
            this.validateStartDate = entity.getValidateStartDate();
            this.validateEndDate = entity.getValidateEndDate();
            this.previewEnabled = entity.getPreviewEnabled();
            this.jumpType = entity.getJumpType();
            this.jumpContent = entity.getJumpContent();
            this.jumpUrl = entity.getJumpUrl();
        }
    }
}
