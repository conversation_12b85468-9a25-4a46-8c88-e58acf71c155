package com.cec.business.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问卷的日期数据统计，适用于date-picker
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormDataStatDateVO {

    /**
     * 填写率（带百分号，如：85.5%）
     */
    private String fillRate;

    /**
     * 填写数量
     */
    private Long fillCount;

    /**
     * 统计类型（year-年度统计，month-按月统计，day-按日统计）
     */
    private String statType;

    /**
     * 图表数据
     */
    private List<FormDataStatDateItemVO> chartData;

    /**
     * 日期统计项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDataStatDateItemVO {

        /**
         * 日期标签（如：2025-04、2025-04-24）
         */
        private String dateLabel;

        /**
         * 该日期的填写数量
         */
        private Integer count;

        /**
         * 该日期的填写率
         */
        private String fillRate;

        /**
         * 日期值（用于排序和计算）
         */
        private String dateValue;
    }
}
