package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.FormTypeEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.util.Date;
import java.util.Map;

/**
 * 表单数据(FormResult)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@FieldNameConstants
@TableName(value = "user_form_data", autoResultMap = true)
public class UserFormDataEntity extends BaseEntity {

    private Long id;
    /**
     * 表单key
     */
    @NotBlank(message = "{user.form.formKey.not.blank}", groups = {AddGroup.class})
    private String formKey;

    /**
     * 提交序号
     */
    private Long serialNumber;

    /**
     * 填写结果原始数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> originalData;


    @NotNull(message = "{user.formData.formType.not.blank}", groups = {AddGroup.class})
    @TableField(exist = false)
    private FormTypeEnum formType;

    /**
     * 填写用户Ua
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> submitUa;

    /**
     * 提交系统
     */
    private String submitOs;

    /**
     * 提交浏览器
     */
    private String submitBrowser;

    /**
     * 提交ip
     */
    private String submitRequestIp;

    /**
     * 提交地址
     */
    private String submitAddress;

    /**
     * 答题开始时间
     */
    private Date startTime;

    /**
     * 答题结束时间
     */
    private Date endTime;

    /***
     * 答题用户id,为空则表示匿名
     */
    @NotNull(message = "{user.formData.userId.not.blank}", groups = {AddGroup.class})
    private Long answerUserId;

    /***
     * 是否匿名(1-是;0-否)
     */
    @NotNull(message = "{user.formData.anonymous.not.blank}", groups = {AddGroup.class})
    @TableField(value = "is_anonymous", typeHandler = BooleanTypeHandler.class)
    private Boolean anonymous;

}
