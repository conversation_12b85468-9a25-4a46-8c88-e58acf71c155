package com.cec.business.domain.struct;

import com.cec.business.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @description : 选项结构
 * @create :  2021/06/07 16:37
 **/

@NoArgsConstructor
@AllArgsConstructor
@Data
public class CheckboxSchemaStruct {

    private Config config;

    public static CheckboxSchemaStruct builder(Map<String, Object> params) {
        return JsonUtils.objToObj(params, CheckboxSchemaStruct.class);
    }

    @Data
    public static class Config extends OptionQuotaListStruct {
        private boolean showVoteResult;
    }
}
