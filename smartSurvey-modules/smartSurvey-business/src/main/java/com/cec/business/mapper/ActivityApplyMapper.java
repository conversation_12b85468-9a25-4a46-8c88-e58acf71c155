package com.cec.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.bo.ActivityApplyEntity;
import com.cec.business.domain.req.ActivityApplyRequest;
import com.cec.business.domain.vo.ActivityApplyVO;
import com.cec.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityApplyMapper extends BaseMapperPlus<ActivityApplyEntity, ActivityApplyVO> {

    List<ActivityApplyVO> selectApplyVoPage(Page<ActivityApplyVO> build, @Param("bo") ActivityApplyRequest bo);
}
