package com.cec.business.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cec.business.domain.bo.ActivityApplyEntity;
import com.cec.business.domain.bo.ActivityEntity;
import com.cec.business.domain.bo.ActivitySigninEntity;
import com.cec.business.domain.enums.ActivityApplyAuditStateEnum;
import com.cec.business.domain.req.ActivityApplyAuditRequest;
import com.cec.business.domain.req.ActivityApplyRequest;
import com.cec.business.domain.req.JoinActivityRequest;
import com.cec.business.domain.vo.ActivityApplyVO;
import com.cec.business.mapper.ActivityApplyMapper;
import com.cec.business.mapper.ActivityMapper;
import com.cec.business.mapper.ActivitySigninMapper;
import com.cec.business.service.ActivityApplyService;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUser;
import com.cec.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 活动报名记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ActivityApplyServiceImpl implements ActivityApplyService {
    private final ActivityApplyMapper baseMapper;
    private final SysUserMapper sysUserMapper;
    private final ActivityMapper activityMapper;
    private final ActivitySigninMapper activitySigninMapper;

    /**
     * 查询活动报名记录
     *
     * @param id 主键
     * @return 活动报名记录
     */
    @Override
    public ActivityApplyVO queryById(Long id) {
        ActivityApplyVO vo = baseMapper.selectVoById(id);
        if (vo != null) {
            SysUser user = sysUserMapper.selectById(vo.getUserId());
            if (user != null) {
                vo.setStaffNum(user.getStaffNum());
                vo.setStaffName(user.getStaffName());
            }
        }
        return vo;
    }

    /**
     * 分页查询活动报名记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动报名记录分页列表
     */
    @Override
    public TableDataInfo<ActivityApplyVO> queryPageList(ActivityApplyRequest bo, PageQuery pageQuery) {
        return TableDataInfo.build(baseMapper.selectApplyVoPage(pageQuery.build(), bo));
    }

    /**
     * 修改活动报名记录
     *
     * @param bo 活动报名记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ActivityApplyEntity bo) {
        if (bo == null) {
            return false;
        }
        ActivityApplyEntity update = MapstructUtils.convert(bo, ActivityApplyEntity.class);
        return baseMapper.updateById(update) > 0;
    }

    /***
     * 活动报名审核
     * @param request    审核的参数
     */
    @Override
    public Boolean audit(ActivityApplyAuditRequest request) {
        long count = baseMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
            .in(ActivityApplyEntity::getId, request.getIds())
            .ne(ActivityApplyEntity::getAuditState, ActivityApplyAuditStateEnum.AUDIT));
        Assert.isTrue(count == 0, () -> new ServiceException("存在状态异常的数据，无法审核"));
        // 批量审核报名记录
        return baseMapper.update(null, Wrappers.<ActivityApplyEntity>lambdaUpdate()
            .in(ActivityApplyEntity::getId, request.getIds())
            .set(ActivityApplyEntity::getAuditState, request.getAuditState())
            .set(ActivityApplyEntity::getAuditRemark, request.getAuditRemark())
            .set(ActivityApplyEntity::getAuditTime, DateUtil.date())
            .set(ActivityApplyEntity::getAuditorId, LoginHelper.getUserId())) > 0;
    }

    /***
     * 报名流程
     * @param param 参与活动的参数
     */
    @RepeatSubmit
    @Override
    public Boolean joinApply(JoinActivityRequest param) {
        if (ObjectUtil.isNull(param.getUserId())) {
            param.setUserId(LoginHelper.getUserId());
        }
        // 查询报名活动
        ActivityEntity activity = getByFormKey(param.getFormKey());
        validateApplyActivity(activity);
        // 校验是否参与过活动
        checkRepeat(activity);
        // 保存报名信息
        return insertActivityApply(param, activity);
    }

    private ActivityEntity getByFormKey(String formKey) {
        return activityMapper.selectOne(Wrappers.<ActivityEntity>lambdaQuery()
            .eq(ActivityEntity::getFormKey, formKey));
    }

    private Boolean insertActivityApply(JoinActivityRequest param, ActivityEntity activity) {
        // 查询用户信息
        SysUser user = sysUserMapper.selectById(param.getUserId());
        Assert.notNull(user, () -> new ServiceException("用户不存在"));
        ActivityApplyEntity apply = new ActivityApplyEntity();
        apply.setActivityId(activity.getId());
        apply.setUserId(param.getUserId());
        if (activity.getApplyAuditEnabled()) {
            apply.setAuditState(ActivityApplyAuditStateEnum.AUDIT);
        } else {
            apply.setAuditState(ActivityApplyAuditStateEnum.PASS);
            apply.setAuditTime(DateUtil.date());
        }
        return baseMapper.insert(apply) > 0;
    }

    private void validateApplyActivity(ActivityEntity activity) {
        Assert.notNull(activity, () -> new ServiceException(MessageUtils.message("activity.apply.not.exists")));
        // 校验是否开启了报名
        Assert.isTrue(activity.getApplyEnabled(), () -> new ServiceException(MessageUtils.message("activity.apply.disabled")));
        boolean isBetween = DateUtil.date().isBefore(activity.getApplyStartTime()) || DateUtil.date().isAfter(activity.getApplyEndTime());
        // 校验是否在报名时间范围内
        Assert.isFalse(isBetween, () -> new ServiceException(MessageUtils.message("activity.apply.time.invalid")));
        // 校验是否开启了报名人数限制
        if (activity.getApplyLimitEnabled()) {
            // 校验是否超过报名人数限制
            long count = baseMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
                .eq(ActivityApplyEntity::getActivityId, activity.getId()));
            Assert.isTrue(count < activity.getApplyLimit(), () -> new ServiceException(MessageUtils.message("activity.apply.limit.exceeded")));
        }
    }

    private void checkRepeat(ActivityEntity activity) {
        long count = baseMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
            .eq(ActivityApplyEntity::getUserId, LoginHelper.getUserId())
            .eq(ActivityApplyEntity::getActivityId, activity.getId()));
        Assert.isTrue(count <= 0, () -> new ServiceException("已存在参与记录"));
    }

    /***
     * 参与签到的流程
     * @param param 签到的参数
     */
    @RepeatSubmit
    @Override
    public Boolean joinSignin(JoinActivityRequest param) {
        param.setUserId(LoginHelper.getUserId());
        // 查询报名活动
        ActivityEntity activity = getByFormKey(param.getFormKey());
        validateSigninActivity(activity);
        // 校验是否重复
        checkSigninRepeat(activity);
        return insertActivitySignin(param, activity);
    }

    private Boolean insertActivitySignin(JoinActivityRequest param, ActivityEntity activity) {
        SysUser user = sysUserMapper.selectById(param.getUserId());
        Assert.notNull(user, () -> new ServiceException(MessageUtils.message("activity.user.not.exists")));
        ActivitySigninEntity sign = new ActivitySigninEntity();
        sign.setUserId(param.getUserId());
        sign.setActivityId(activity.getId());
        sign.setSigninIp(param.getSigninIp());
        return activitySigninMapper.insert(sign) > 0;
    }

    private void checkSigninRepeat(ActivityEntity activity) {
        long count = activitySigninMapper.selectCount(Wrappers.<ActivitySigninEntity>lambdaQuery()
            .eq(ActivitySigninEntity::getUserId, LoginHelper.getUserId())
            .eq(ActivitySigninEntity::getActivityId, activity.getId()));
        Assert.isTrue(count == 0, () -> new ServiceException(MessageUtils.message("activity.signin.already.exists")));
    }

    private void validateSigninActivity(ActivityEntity activity) {
        Assert.notNull(activity, () -> new ServiceException(MessageUtils.message("activity.signin.not.exists")));
        // 校验是否开启了签到
        Assert.isTrue(activity.getSigninEnabled(), () -> new ServiceException(MessageUtils.message("activity.signin.disabled")));
        boolean isBetween = DateUtil.date().isBefore(activity.getSigninStartTime()) || DateUtil.date().isAfter(activity.getSigninEndTime());
        Assert.isFalse(isBetween, () -> new ServiceException(MessageUtils.message("activity.signin.time.invalid")));
        if (activity.getApplySigninEnabled()) {
            // 开启报名可签到，校验是否报名过且通过审核
            long count = baseMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
                .eq(ActivityApplyEntity::getActivityId, activity.getId())
                .eq(ActivityApplyEntity::getUserId, LoginHelper.getUserId())
                .eq(ActivityApplyEntity::getAuditState, ActivityApplyAuditStateEnum.PASS));
            Assert.isTrue(count > 0, () -> new ServiceException(MessageUtils.message("activity.apply.not.approved")));
        }
    }
}
