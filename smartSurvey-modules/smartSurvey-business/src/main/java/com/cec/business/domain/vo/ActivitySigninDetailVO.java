package com.cec.business.domain.vo;

import com.cec.business.domain.enums.ActivitySignStatusEnum;
import com.cec.business.domain.enums.ActivityStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动主视图对象 activity
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class ActivitySigninDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 自定义富文本
     */
    private String richDescription;

    /**
     * 封面图
     */
    private String coverImg;

    /***
     * 签到状态
     */
    private ActivitySignStatusEnum signStatus = ActivitySignStatusEnum.SIGN;

    /***
     * 签到时间
     */
    private Date signinedTime;

    /**
     * 签到开始时间
     */
    private Date signinStartTime;

    /**
     * 签到结束时间
     */
    private Date signinEndTime;

    /**
     * 活动状态(0-草稿;1-进行中;2-已结束;3-已关闭)
     */
    private ActivityStatusEnum status;
}


