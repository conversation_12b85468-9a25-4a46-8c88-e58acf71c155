package com.cec.business.domain.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/***
 * 报名的相关参数
 */
@Data
public class JoinActivityRequest implements Serializable {

    /***
     * 当前登录的用户id (前端可不传)
     */
    private Long userId;

    /***
     * 参与的活动id
     */
    private Long activityId;

    /***
     * 参与的活动formKey
     */
    @NotNull(message = "{validation.activity.form.key.not.null}")
    private String formKey;

    /***
     * 签到IP地址
     */
    private String signinIp;

}
