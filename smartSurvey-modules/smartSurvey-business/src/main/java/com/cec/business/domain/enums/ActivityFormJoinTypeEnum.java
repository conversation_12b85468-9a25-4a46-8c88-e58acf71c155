package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 问卷类型(1-报名后;2-签到后)
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ActivityFormJoinTypeEnum implements IDictEnum<Integer> {
    APPLY_AFTER(1, "报名后"),
    SIGNIN_AFTER(2, "签到后"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static ActivityFormJoinTypeEnum findByValue(Integer value) {
        for (ActivityFormJoinTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static ActivityFormJoinTypeEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
