package com.cec.business.domain.vo;

import com.cec.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * @description : 回收站表单
 * @create :  2021/03/23 17:13
 **/
@Data
@AllArgsConstructor
public class RecycleFormVO extends BaseEntity {
    private String key;
    /***
     * 收集数量
     */
    private Long resultCount;

    private String name;

    public RecycleFormVO(String key, Long resultCount, String name, Date createTime, Date updateTime) {
        this.key = key;
        this.resultCount = resultCount;
        this.name = name;
        this.setCreateTime(createTime);
        this.setUpdateTime(updateTime);
    }
}
