package com.cec.business.domain.req;

import com.cec.business.domain.enums.ActivityApplyAuditStateEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

@Data
public class ActivityApplyAuditRequest implements Serializable {

    /**
     * 报名记录ID
     */
    @NotNull(message = "{validation.apply.record.ids.not.null}")
    private List<Long> ids;

    /**
     * 审核状态
     */
    @NotNull(message = "{validation.audit.state.not.null}")
    private ActivityApplyAuditStateEnum auditState;

    /**
     * 审核备注
     */
    @Length(max = 200, message = "{validation.audit.remark.length.max}")
    private String auditRemark;

}
