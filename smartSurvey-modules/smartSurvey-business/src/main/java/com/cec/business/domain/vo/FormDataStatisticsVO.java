package com.cec.business.domain.vo;

import com.cec.business.domain.bo.UserFormItemEntity;
import com.cec.business.domain.enums.FormCollectLimitTypeEnum;
import com.cec.business.domain.enums.FormStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 表单数据统计
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Accessors(chain = true)
public class FormDataStatisticsVO {
    /**
     * 问卷id
     */
    private Long formId;

    /**
     * 问卷key
     */
    private String formKey;

    /**
     * 问卷标题
     */
    private String formName;

    /**
     * 问卷状态
     */
    private FormStatusEnum status;

    /**
     * 答题是否有有效期(1-是；0-否)
     */
    private Boolean validateEnabled;

    /**
     * 答题开始时间
     */
    private Date validateStartDate;

    /**
     * 答题结束时间
     */
    private Date validateEndDate;

    /**
     * 答题类型（0-都可答题;1-指定人）
     */
    private FormCollectLimitTypeEnum collectLimitType;

    /**
     * 指定填写人数
     */
    private Integer collectLimitCount;

    /**
     * 填写人数
     */
    private Integer collectCount;


    /**
     * 问卷组件列表
     */
    private List<UserFormItemEntity> formItemList;


}
