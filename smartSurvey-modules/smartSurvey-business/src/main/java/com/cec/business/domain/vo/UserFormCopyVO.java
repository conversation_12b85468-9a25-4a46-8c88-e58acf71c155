package com.cec.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表单表(Form)表实体类
 */
@Data
@FieldNameConstants
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFormCopyVO implements Serializable {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 表单名称
     */
    private String name;

    /**
     * 表单描述
     */
    private String description;

    /**
     * 表单来源
     */
    private Integer sourceType;

    /**
     * 来源ID
     */
    private String sourceId;

    /**
     * 表单类型
     */
    private Integer type;

    /**
     * 是否可收集(1-是；0-否)
     */
    private Boolean collectEnabled;

    /**
     * 答题类型（0-都可答题;1-指定人）
     */
    private Integer collectLimitType;

    /**
     * 是否需要登录(1-是；0-否)
     */
    private Boolean needLogin;

    /**
     * 匿名是否可填写(1-是；0-否)
     */
    private Boolean anonymousEnabled;

    /**
     * 是否限制答题次数(1-是；0-否)
     */
    private Boolean answerCountEnabled;

    /**
     * 答题次数
     */
    private Integer answerCount;

    /**
     * 设定问卷总数量上限(1-是；0-否)
     */
    private Boolean answerTotalCountEnabled;

    /**
     * 问卷总数量上限
     */
    private Integer answerTotalCount;

    /**
     * 答题是否有有效期(1-是；0-否)
     */
    private Boolean validateEnabled;

    /**
     * 答题开始时间
     */
    private Date validateStartDate;

    /**
     * 答题结束时间
     */
    private Date validateEndDate;

    /**
     * 是否可预览答卷(1-是；0-否)
     */
    private Boolean previewEnabled;

    /**
     * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
     */
    private Integer jumpType;

    /**
     * 跳转内容
     */
    private String jumpContent;

    /**
     * 指定页面路径
     */
    private String jumpUrl;

}
