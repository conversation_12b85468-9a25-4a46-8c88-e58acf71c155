package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.util.Map;

/**
 * 表单设置对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@TableName(value = "user_form_setting", autoResultMap = true)
public class UserFormSettingEntity extends BaseEntity {

    private Long id;

    @NotBlank
    private String formKey;


    /**
     * 设置具体内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> settings;


}
