package com.cec.business.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.GenericConverter;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Set;

/***
 * 将前端的字符串转换为对应的枚举类型
 */
@Component
public class StringToEnumConverter implements GenericConverter {

    @Override
    public Set<ConvertiblePair> getConvertibleTypes() {
        return Collections.singleton(new ConvertiblePair(String.class, Enum.class));
    }

    @Override
    public Object convert(Object source, @NotNull TypeDescriptor sourceType, @NotNull TypeDescriptor targetType) {
        if (source == null) {
            return null;
        }

        Class<?> enumType = targetType.getType();
        if (enumType.isEnum()) {
            String value = source.toString();
            try {
                // 尝试调用fromString方法
                Method method = enumType.getMethod("fromString", String.class);
                return method.invoke(null, value);
            } catch (Exception e) {
                // 如果没有fromString方法，则使用默认的valueOf
                return Enum.valueOf((Class<? extends Enum>) enumType, value);
            }
        }
        return null;
    }
}
