package com.cec.business.domain.req;

import com.cec.business.domain.enums.ActivityStatusEnum;
import com.cec.business.domain.enums.FormCollectLimitTypeEnum;
import com.cec.business.domain.enums.FormJumpTypeEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动主业务对象 activity
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class ActivityReq {

    /**
     * 活动ID
     */
    @NotNull(message = "{common.id.not.blank}", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动名称
     */
    @NotBlank(message = "{activity.form.name.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 自定义富文本
     */
    private String richDescription;

    /**
     * 封面图
     */
    private String coverImg;

    /**
     * 开启报名(1-是;0-否)
     */
    private Boolean applyEnabled;

    /**
     * 报名开始时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date applyStartTime;

    /**
     * 报名结束时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date applyEndTime;

    /**
     * 报名类型（0-所有人;1-指定人）
     */
    private FormCollectLimitTypeEnum applyLimitType;

    /**
     * 报名审核(1-是;0-否)
     */
    private Boolean applyAuditEnabled;

    /**
     * 是否限制报名人数(1-是;0-否)
     */
    private Boolean applyLimitEnabled;

    /**
     * 报名人数限制(0-无限制)
     */
    private Integer applyLimit;

    /***
     * 报名可签到(1-是;0-否)
     */
    private Boolean applySigninEnabled;

    /**
     * 开启签到(1-是;0-否)
     */
    private Boolean signinEnabled;

    /**
     * 签到开始时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date signinStartTime;

    /**
     * 签到结束时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date signinEndTime;

    /**
     * 开启问卷收集(1-是;0-否)
     */
    private Boolean formEnabled;

    /***
     * 报名可填写
     */
    private Boolean formJoinApplyEnabled;

    /***
     * 签到可填写
     */
    private Boolean formJoinSigninEnabled;

    /**
     * 问卷模板ID(选中问卷模版的Id)
     */
    private Long formTemplateId;

    /**
     * 问卷模板formKey
     */
    private String formTemplateKey;

    /**
     * 问卷表单KEY(新增时不传，系统自动生成)
     */
    private String formKey;

    /**
     * 表单标题
     */
    private String formTitle;

    /**
     * 表单标题描述
     */
    private String formDescription;

    /**
     * 问卷开始时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date formStartTime;

    /**
     * 问卷结束时间(yyyy-MM-dd HH:mm:ss)
     */
    private Date formEndTime;

    /**
     * 活动状态(如为空，则默认"未发布"，否则为传的值)
     */
    private ActivityStatusEnum status;

    /***
     * 签到url(新增时不传，系统自动生成)
     */
    private String signinUrl;

    /***
     * 报名url(新增时不传，系统自动生成)
     */
    private String applyUrl;

    /***
     * 问卷url(新增时不传，系统自动生成)
     */
    private String formUrl;

    /**
     * 是否可预览答卷(1-是；0-否)
     */
    private Boolean previewEnabled;

    /**
     * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
     */
    private FormJumpTypeEnum jumpType;

    /**
     * 跳转内容
     */
    private String jumpContent;

    /**
     * 指定页面路径
     */
    private String jumpUrl;

    /**
     * 活动管理员IDs
     */
    @NotEmpty(message = "{validation.activity.managers.not.empty}", groups = {AddGroup.class, EditGroup.class})
    private Long[] activityManagerUserIds;

    /**
     * 活动成员IDs
     */
    // @NotEmpty(message = "项目成员不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long[] activityMemberUserIds;

    /**
     * 活动成员部门IDs
     */
    // @NotEmpty(message = "项目成员部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long[] activityDeptIds;

    /***
     * 指定名单(用户ID列表)
     */
    private List<Long> userIdList;

    /***
     * 指定名单（部门ID列表）
     */
    private List<Long> deptIdList;

}
