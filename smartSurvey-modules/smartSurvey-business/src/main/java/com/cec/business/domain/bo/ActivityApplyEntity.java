package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.business.domain.enums.ActivityApplyAuditStateEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动报名记录对象 activity_apply
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity_apply")
@AutoMapper(target = ActivityApplyEntity.class, reverseConvertGenerate = false)
public class ActivityApplyEntity extends BaseEntity {

    /**
     * 报名ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID(系统用户)
     */
    @NotNull(message = "{validation.user.id.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 审核状态(0-待审核;1-已通过;2-已拒绝)
     */
    private ActivityApplyAuditStateEnum auditState;

    /**
     * 审核备注
     */
    @NotBlank(message = "{validation.audit.remark.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String auditRemark;

    /**
     * 审核人ID
     */
    @NotNull(message = "{validation.auditor.id.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Long auditorId;

    /**
     * 审核时间
     */
    @NotNull(message = "{validation.audit.time.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Date auditTime;


}
