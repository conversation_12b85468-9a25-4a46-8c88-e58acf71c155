package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description : 表单类型
 * @create : 2020-12-04 13:35
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FormTypeEnum implements IDictEnum<Integer> {


    /**
     * 普通表单
     */
    ORDINARY(1, "普通表单"),

    /***
     * 活动表单
     */
    ACTIVITY(2, "活动表单"),
    ;

    @EnumValue
    private final Integer value;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static FormTypeEnum findByValue(Integer value) {
        for (FormTypeEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static FormTypeEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
