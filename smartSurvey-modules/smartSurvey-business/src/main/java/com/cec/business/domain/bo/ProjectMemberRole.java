package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 项目成员角色对象 project_member_role
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableName("project_member_role")
@Accessors(chain = true)
public class ProjectMemberRole {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 项目ID
     */
    // @TableId(value = "project_id")
    private Long projectId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;


}
