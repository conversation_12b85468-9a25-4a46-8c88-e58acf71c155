package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * 答题详细信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@FieldNameConstants
@TableName(value = "user_form_data_detail", autoResultMap = true)
public class UserFormDataDetailEntity extends BaseEntity {

    private Long id;
    /**
     * 表单key
     */
    @NotBlank(message = "{validation.request.error}")
    private String formKey;

    /**
     * 回答id
     */
    private Long dataId;

    /***
     * 表单项id
     */
    private String formItemId;

    /**
     * 答题类型
     */
    private String itemType;

    /**
     * 答题值
     */
    private String answerValue;

}
