package com.cec.business.controller;

import com.cec.business.domain.bo.FormTemplateEntity;
import com.cec.business.domain.req.QueryFormTemplateRequest;
import com.cec.business.domain.vo.FormTemplateVO;
import com.cec.business.service.FormTemplateService;
import com.cec.common.core.domain.R;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 表单模板接口
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/form/template/")
public class FormTemplateController {
    private final FormTemplateService formTemplateService;

    /**
     * 分页查询问卷模板
     */
    @GetMapping("page")
    public TableDataInfo<FormTemplateVO> queryFormTemplates(QueryFormTemplateRequest request, PageQuery pageQuery) {
        return formTemplateService.selectFormTemplatePageList(request, pageQuery);
    }

    /**
     * 表单另存为模板
     */
    @Log(title = "问卷模版", businessType = BusinessType.INSERT)
    @PostMapping("create")
    public R<String> createFormTemplate(@RequestBody FormTemplateEntity formTemplateEntity) {
        return R.ok(formTemplateService.createFormTemplate(formTemplateEntity).getFormKey());
    }

    /**
     * 根据模版key删除模版
     */
    @Log(title = "问卷模版", businessType = BusinessType.DELETE)
    @GetMapping("delete/{key}")
    public R<Void> deleteByKey(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        formTemplateService.deleteByKey(key);
        return R.ok();
    }

    /**
     * 查询问卷模板详情
     */
    @GetMapping("details/{key}")
    public R<FormTemplateEntity.Definition> queryFormTemplateDetails(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return R.ok(formTemplateService.getTemplateDefinition(key));
    }

}
