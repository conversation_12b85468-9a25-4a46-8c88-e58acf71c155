package com.cec.business.service;

import com.cec.business.domain.bo.ActivitySigninEntity;
import com.cec.business.domain.req.ActivitySigninRequest;
import com.cec.business.domain.vo.ActivitySigninVO;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

public interface ActivitySigninService {
    /**
     * 查询活动签到记录
     *
     * @param id 主键
     * @return 活动签到记录
     */
    ActivitySigninVO queryById(Long id);

    /**
     * 分页查询活动签到记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动签到记录分页列表
     */
    TableDataInfo<ActivitySigninVO> queryPageList(ActivitySigninRequest bo, PageQuery pageQuery);

    /**
     * 修改活动签到记录
     *
     * @param bo 活动签到记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ActivitySigninEntity bo);

}
