package com.cec.business.controller;

import com.cec.business.domain.req.ProjectInfoReq;
import com.cec.business.domain.vo.ProjectInfoVo;
import com.cec.business.service.IProjectInfoService;
import com.cec.common.core.domain.R;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.web.core.BaseController;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 项目信息
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/project/info")
public class ProjectInfoController extends BaseController {

    private final IProjectInfoService projectInfoService;

    /**
     * 查询项目信息列表
     */
    //@SaCheckPermission("system:info:list")
    @GetMapping("/list")
    public List<ProjectInfoVo> list(ProjectInfoReq bo) {
        return projectInfoService.queryPageList(bo);
    }

    /**
     * 获取项目信息详细信息
     *
     * @param projectId 主键
     */
    //@SaCheckPermission("system:info:query")
    @GetMapping("/{projectId}")
    public R<ProjectInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long projectId) {
        return R.ok(projectInfoService.queryById(projectId));
    }

    /**
     * 新增项目信息
     */
    //@SaCheckPermission("system:info:add")
    @Log(title = "项目信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProjectInfoReq req) {
        return toAjax(projectInfoService.insertByBo(req));
    }

    /**
     * 修改项目信息
     */
    //@SaCheckPermission("system:info:edit")
    @Log(title = "项目信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProjectInfoReq req) {
        return toAjax(projectInfoService.updateByBo(req));
    }

    /**
     * 删除项目信息
     *
     * @param projectIds 主键串
     */
    //@SaCheckPermission("system:info:remove")
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectId}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] projectIds) {
        return toAjax(projectInfoService.deleteWithValidByIds(List.of(projectIds), true));
    }

    /**
     * 获取项目成员roleKey
     * 1、项目成员-project_member
     * 2、项目管理员-project_manager
     * 3、超管理员-superadmin
     *
     * @param projectId 项目Id
     */
    //@SaCheckPermission("system:info:remove")
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
    @GetMapping("/{projectId}/roleKey")
    public R<String> remove(@NotNull(message = "主键不能为空") @PathVariable Long projectId) {
        return R.ok(projectInfoService.checkProjectRoleKey(projectId));
    }
}
