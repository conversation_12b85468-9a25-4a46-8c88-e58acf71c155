package com.cec.business.domain.bo;


import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动签到记录业务对象 activity_signin
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ActivitySigninEntity.class, reverseConvertGenerate = false)
@TableName("activity_signin")
public class ActivitySigninEntity extends BaseEntity {

    /**
     * 签到ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 报名ID
     */
    private Long applyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 签到IP
     */
    @NotBlank(message = "{validation.signin.ip.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String signinIp;

    /**
     * 问卷数据ID(关联user_form_data)
     */
    @NotNull(message = "{validation.form.data.id.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Long formDataId;
}
