package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * 表单授权对象对象 user_Form_auth
 * 授权组授权对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@FieldNameConstants
@TableName(value = "user_form_auth", autoResultMap = true)
public class UserFormAuthEntity extends BaseEntity {

    private Long id;

    /**
     * 项目key
     */
    @NotBlank
    private String formKey;

    /**
     * 授权组Id
     */
    @NotNull
    private Long authGroupId;

    /**
     * 用户Id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> userIdList;

    /**
     * 角色Id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> roleIdList;

    /**
     * 部门Id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> deptIdList;


}
