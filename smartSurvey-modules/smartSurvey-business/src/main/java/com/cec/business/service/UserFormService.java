package com.cec.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.domain.req.QueryFormRequest;
import com.cec.business.domain.vo.UserFormDetailVO;
import com.cec.business.domain.vo.UserFormVO;
import com.cec.common.core.domain.R;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

/**
 * 表单表(Form)表服务接口
 *
 * @since 2020-11-18 18:16:18
 */
public interface UserFormService extends IService<UserFormEntity> {

    /**
     * 根据key获取
     *
     * @param key key
     * @return UserFormEntity
     */
    UserFormEntity getByKey(final String key);

    /***
     * 问卷分页查询
     * @param request   查询条件
     * @param pageQuery 分页参数
     * @return 问卷分页列表
     */
    TableDataInfo<UserFormVO> selectFormPageList(QueryFormRequest request, PageQuery pageQuery);

    /***
     * 发布表单
     * @param key    表单key
     */
    Boolean publishForm(String key);

    /**
     * 停止收集表单
     */
    Boolean stopForm(String key);

    /**
     * 创建表单
     *
     * @param form 表单信息
     * @return 表单信息
     */
    R<UserFormEntity> createForm(UserFormEntity form);

    /***
     * 逻辑删除表单
     */
    Boolean logicDeleteForm(String key);

    /**
     * 删除表单
     */
    Boolean deleteForm(String key);

    /**
     * 修改表单
     *
     * @param form 表单信息
     */
    Boolean updateForm(UserFormEntity form);

    /***
     * 查询表单详情
     * @param key   表单key
     * @return 表单详情
     */
    R<UserFormDetailVO> queryFormDetails(String key);
}
