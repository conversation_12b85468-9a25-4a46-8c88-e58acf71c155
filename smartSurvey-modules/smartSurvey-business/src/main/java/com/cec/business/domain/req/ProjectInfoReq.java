package com.cec.business.domain.req;


import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 项目信息业务对象 project_info
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInfoReq extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "{validation.project.id.not.null}", groups = {EditGroup.class})
    private Long projectId;

    /**
     * 项目名称
     */
    @NotBlank(message = "{validation.project.name.not.blank}", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "{validation.project.name.length.max}", groups = {AddGroup.class, EditGroup.class})
    private String projectName;

    /**
     * 项目管理员IDs
     */
    @NotEmpty(message = "{validation.project.managers.not.empty}", groups = {AddGroup.class, EditGroup.class})
    private Long[] projectManagerUserIds;

    /**
     * 项目成员IDs
     */
    // @NotEmpty(message = "项目成员不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long[] projectMemberUserIds;

    /**
     * 项目成员部门IDs
     */
    // @NotEmpty(message = "项目成员部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long[] projectDeptIds;

}
