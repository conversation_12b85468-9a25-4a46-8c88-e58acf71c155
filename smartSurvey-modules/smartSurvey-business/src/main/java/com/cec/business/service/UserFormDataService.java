package com.cec.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cec.business.domain.bo.UserFormDataEntity;
import com.cec.business.domain.req.UserFormDataAnswerRequest;
import com.cec.business.domain.req.UserFormDataSearchRequest;
import com.cec.business.domain.vo.FormAnswerVO;
import com.cec.business.domain.vo.FormDataStatDateVO;
import com.cec.business.domain.vo.FormDataStatFileVO;
import com.cec.business.domain.vo.FormDataStatSelectVO;
import com.cec.business.domain.vo.FormDataStatTextVO;
import com.cec.business.domain.vo.FormDataStatisticsVO;
import com.cec.business.domain.vo.FormDataTableVO;
import com.cec.common.mybatis.core.page.PageQuery;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 表单表单项(FormResult)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-23 14:09:22
 */
public interface UserFormDataService extends IService<UserFormDataEntity> {

    /***
     * 回答表单
     * @param req    表单数据
     */
    FormAnswerVO answerUserForm(UserFormDataAnswerRequest req);

    /***
     * 问卷的数据详情
     * @param req    详情查询参数
     * @return 详情数据
     */
    FormDataTableVO answerUserFormDetail(UserFormDataSearchRequest req);

    FormDataStatisticsVO answerStatistics(String formId);

    FormDataStatTextVO answerStatisticsText(String formItemId, String formKey, PageQuery pageQuery);

    FormDataStatSelectVO answerStatisticsSelect(String formItemId, String formKey);

    /**
     * 问卷的日期数据统计，适用于date-picker
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     * @param statType 统计类型（year-年度统计，month-按月统计，day-按日统计）
     * @return 日期统计结果
     */
    FormDataStatDateVO answerStatisticsDate(String formItemId, String formKey, String statType);

    /**
     * 问卷的文件数据统计，适用于upload
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     * @param pageQuery 分页查询参数
     * @return 文件统计结果
     */
    FormDataStatFileVO answerStatisticsFile(String formItemId, String formKey, PageQuery pageQuery);

    /**
     * 下载表单项的所有附件为压缩包
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     * @param response HTTP响应对象
     * @throws Exception 下载异常
     */
    void downloadAllFiles(String formItemId, String formKey, HttpServletResponse response) throws Exception;
}
