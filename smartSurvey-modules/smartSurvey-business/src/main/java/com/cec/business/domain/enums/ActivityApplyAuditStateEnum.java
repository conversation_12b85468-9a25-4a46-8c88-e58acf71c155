package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报名审核状态
 * 审核状态(0-待审核;1-已通过;2-已拒绝)
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ActivityApplyAuditStateEnum implements IDictEnum<Integer> {
    AUDIT(0, "待审核"),
    PASS(1, "已通过"),
    REJECT(2, "已拒绝"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static ActivityApplyAuditStateEnum findByValue(Integer value) {
        for (ActivityApplyAuditStateEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static ActivityApplyAuditStateEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
