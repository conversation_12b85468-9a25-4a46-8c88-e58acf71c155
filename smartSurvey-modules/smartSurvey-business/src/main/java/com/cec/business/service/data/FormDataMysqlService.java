package com.cec.business.service.data;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cec.business.domain.bo.UserFormDataEntity;
import com.cec.business.domain.req.UserFormDataSearchRequest;
import com.cec.business.domain.vo.FormDataTableVO;
import com.cec.business.mapper.UserFormDataMapper;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单数据基础服务
 **/
@Service
@Slf4j
public class FormDataMysqlService extends FormDataBaseService {
    @Resource
    private UserFormDataMapper userFormDataMapper;

    private static final String USER_FORM_DATA = "user_form_data";

    @Override
    public FormDataTableVO search(UserFormDataSearchRequest request) {
        // 校验formKey只允许存在字符串和数字
        if (StrUtil.isBlank(request.getFormKey()) || !request.getFormKey().matches("^[a-zA-Z0-9]+$")) {
            return new FormDataTableVO();
        }
        // 拼接sql
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select * from ")
            .append(USER_FORM_DATA)
            .append(" where form_key = '")
            .append(request.getFormKey())
            .append("'");
        if (ObjectUtil.isNotNull(request.getAnswerUserId())) {
            sqlBuilder.append(" and answer_user_id = ").append(request.getAnswerUserId());
        }
        if (ObjectUtil.isNotNull(request.getStartTime()) && ObjectUtil.isNotNull(request.getEndTime())) {
            sqlBuilder.append(" and create_time between '")
                .append(request.getStartTime())
                .append("' and '")
                .append(request.getEndTime())
                .append("'");
        }
        //1. 拼接条件 查询条件 用大括号包起来 里面的条件会拼接 OR 或者 AND 不能影响其他默认附带条件 比如form_key 否则会错误查询
        StringBuilder whereBuilder = new StringBuilder();
        // 查询指定id数据
        if (ObjectUtil.isNotNull(request.getDataIds()) && !request.getDataIds().isEmpty()) {
            String ids = CollUtil.join(request.getDataIds(), ",");
            if (!ids.matches("^[\\d,]+$")) {
                throw new ServiceException(MessageUtils.message("system.invalid.parameter"));
            }
            whereBuilder.append(" and id in (").append(ids).append(")");
        }
        // 先查询总数，查询总数后再进行拼接order by 及 limit 语句
        StringBuilder countBuilder = new StringBuilder("select count(1) from ")
            .append(USER_FORM_DATA)
            .append(" where form_key = '")
            .append(request.getFormKey())
            .append("'");
        countBuilder.append(whereBuilder);
        Long total = userFormDataMapper.selectCountBySql(countBuilder.toString());

        whereBuilder.append(" ORDER BY id DESC");
        // 分页
        if (ObjectUtil.isNotNull(request.getPageNum()) && ObjectUtil.isNotNull(request.getPageSize())) {
            whereBuilder.append(" limit ")
                .append(request.getPageNum() * request.getPageSize())
                .append(",")
                .append(request.getPageSize());
        }
        sqlBuilder.append(whereBuilder);
        List<UserFormDataEntity> userFormDataEntities = userFormDataMapper.selectRowsBySql(sqlBuilder.toString());

        // 过滤指定字段
        List<Map<String, Object>> maps = expandData(userFormDataEntities, request.getFilterFields());
        return new FormDataTableVO(maps, total);
    }

    /**
     * 展开数据为一级
     */
    public List<Map<String, Object>> expandData(List<UserFormDataEntity> userFormDataEntities, String[] filterFields) {
        return userFormDataEntities.stream().map(item -> {
            Map<String, Object> processData = item.getOriginalData();
            Map<String, Object> resultMap = BeanUtil.beanToMap(item);
            resultMap.remove(UserFormDataEntity.Fields.originalData);
            resultMap.put(BaseEntity.Fields.createTime, item.getCreateTime());
            resultMap.put(BaseEntity.Fields.updateTime, item.getUpdateTime());
            processData.putAll(resultMap);
            // 只过滤指定字段
            if (filterFields != null) {
                Map<String, Object> filterMap = MapUtil.newHashMap();
                for (String filterField : filterFields) {
                    filterMap.put(filterField, processData.get(filterField));
                }
                return filterMap;
            }
            return processData;
        }).toList();
    }
}
