package com.cec.business.domain.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.FormTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/***
 * 问卷表单答题请求
 */
@Data
public class UserFormDataAnswerRequest implements Serializable {

    /**
     * 表单key
     */
    @NotBlank(message = "{user.form.formKey.not.blank}")
    private String formKey;

    /**
     * 填写结果原始数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> originalData;

    @NotNull(message = "{user.formData.formType.not.blank}")
    private FormTypeEnum formType;

    /***
     * 答题用户id,为空则表示匿名
     */
    private Long answerUserId;

    /**
     * 提交ip
     */
    private String submitRequestIp;

    /**
     * 提交浏览器
     */
    private String submitBrowser;

    /***
     * 是否匿名(1-是;0-否)
     */
    @NotNull(message = "{user.formData.anonymous.not.blank}")
    private Boolean anonymous;

    /**
     * 答题开始时间
     */
    private Date startTime;

    /**
     * 答题结束时间
     */
    private Date endTime;


}
