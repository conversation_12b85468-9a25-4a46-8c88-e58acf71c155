package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * 表单逻辑(UserFormLogic)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "user_form_logic", autoResultMap = true)
public class UserFormLogicEntity extends BaseEntity {

    private Long id;

    /**
     * 表单key
     */
    @NotBlank(message = "{validation.form.key.not.blank}")
    private String formKey;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Definition> scheme;

    /**
     * 逻辑定义对象
     */
    @Data
    public static class Definition {

        /**
         * 触发内容
         */
        private Set<Trigger> triggerList;
        /**
         * 条件
         */
        private Set<Condition> conditionList;
    }


    @Data
    public static class Trigger {
        /**
         * 表单项Id
         */
        private String formItemId;
        /**
         * 类型
         */
        private String type = "show";
    }

    /**
     * 条件
     */
    @Data
    public static class Condition {
        /**
         * 表单项Id
         */
        private String formItemId;
        /**
         * 表达式
         */
        private String expression;
        /**
         * 选项
         */
        private Object optionValue;


        /**
         * AND OR
         */
        private String relation;

    }

}
