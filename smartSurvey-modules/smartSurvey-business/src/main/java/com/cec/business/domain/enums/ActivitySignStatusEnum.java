package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 签到状态
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ActivitySignStatusEnum implements IDictEnum<Integer> {
    CLOSE(-1, "未开启"),
    SIGN(0, "可签到"),
    NEED_APPLY(1, "需报名"),
    SIGNED(2, "已签到"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static ActivitySignStatusEnum findByValue(Integer value) {
        for (ActivitySignStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static ActivitySignStatusEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
