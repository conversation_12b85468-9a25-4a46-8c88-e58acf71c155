package com.cec.business.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.cec.business.domain.bo.ActivityApplyEntity;
import com.cec.business.domain.enums.ActivityApplyAuditStateEnum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动报名记录视图对象 activity_apply
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActivityApplyEntity.class)
public class ActivityApplyVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报名ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID(系统用户)
     */
    private Long userId;

    /**
     * 审核状态(0-待审核;1-已通过;2-已拒绝)
     */
    private ActivityApplyAuditStateEnum auditState;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /***
     * 签到时间
     */
    private Date createTime;

    /***
     * 员工编号
     */
    private String staffNum;

    /***
     * 员工姓名
     */
    private String staffName;

    /***
     * 审核人姓名
     */
    private String auditName;
}
