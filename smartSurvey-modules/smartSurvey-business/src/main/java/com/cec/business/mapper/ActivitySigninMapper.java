package com.cec.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.bo.ActivitySigninEntity;
import com.cec.business.domain.req.ActivitySigninRequest;
import com.cec.business.domain.vo.ActivitySigninVO;
import com.cec.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

public interface ActivitySigninMapper extends BaseMapperPlus<ActivitySigninEntity, ActivitySigninVO> {

    Page<ActivitySigninVO> selectSigninVoPage(Page<ActivitySigninVO> page, @Param("bo") ActivitySigninRequest bo);
}
