package com.cec.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.domain.bo.UserFormSettingEntity;
import com.cec.business.domain.struct.FormSettingSchemaStruct;
import com.cec.business.mapper.UserFormSettingMapper;
import com.cec.business.service.UserFormSettingService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class UserFormSettingServiceImpl extends ServiceImpl<UserFormSettingMapper, UserFormSettingEntity> implements UserFormSettingService {
    @Override
    public Boolean saveFormSetting(Map<String, Object> params) {
        return null;
    }

    @Override
    public UserFormSettingEntity getFormSettingByKey(String formKey) {
        return null;
    }

    @Override
    public FormSettingSchemaStruct getFormSettingSchema(String formKey) {
        return null;
    }

    @Override
    public Boolean getUserFormWriteSettingStatus(String formKey, String requestIp, String wxOpenId, Integer type) {
        return null;
    }

    @Override
    public Boolean deleteAllSetting(String key) {
        return null;
    }
}
