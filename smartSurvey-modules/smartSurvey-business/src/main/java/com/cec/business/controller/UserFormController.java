package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.domain.bo.UserFormItemEntity;
import com.cec.business.domain.bo.UserFormLogicEntity;
import com.cec.business.domain.req.QueryFormItemRequest;
import com.cec.business.domain.req.QueryFormRequest;
import com.cec.business.domain.req.SortFormItemRequest;
import com.cec.business.domain.vo.OperateFormItemVO;
import com.cec.business.domain.vo.UserFormDetailVO;
import com.cec.business.domain.vo.UserFormVO;
import com.cec.business.service.UserFormItemService;
import com.cec.business.service.UserFormLogicService;
import com.cec.business.service.UserFormService;
import com.cec.common.core.domain.R;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户表单 API 接口
 **/
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/user/form/")
public class UserFormController extends BaseController {
    private final UserFormService formService;
    private final UserFormItemService formItemService;
    private final UserFormLogicService userFormLogicService;

    /**
     * 根据条件查询所有问卷
     */
    @GetMapping("page")
    public TableDataInfo<UserFormVO> listForms(QueryFormRequest request, PageQuery pageQuery) {
        return formService.selectFormPageList(request, pageQuery);
    }

    /**
     * 创建问卷
     */
    @Log(title = "智能问卷", businessType = BusinessType.INSERT)
    @PostMapping("create")
    public R<UserFormEntity> createForm(@RequestBody UserFormEntity form) {
        return formService.createForm(form);
    }

    /**
     * 问卷更新
     */
    @Log(title = "智能问卷", businessType = BusinessType.UPDATE)
    @PostMapping("update")
    public R<Void> updateForm(@RequestBody UserFormEntity form) {
        return toAjax(formService.updateForm(form));
    }

    /**
     * 查询问卷
     */
    @GetMapping("{key}")
    public R<UserFormEntity> queryFormByKey(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return R.ok(formService.getByKey(key));
    }

    /**
     * 发布问卷
     */
    @Log(title = "智能问卷", businessType = BusinessType.UPDATE)
    @GetMapping("publish/{key}")
    public R<Void> publishForm(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return toAjax(formService.publishForm(key));
    }

    /**
     * 暂停/开始收集
     */
    @Log(title = "智能问卷", businessType = BusinessType.UPDATE)
    @GetMapping("stop/{key}")
    public R<Void> stopForm(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return toAjax(formService.stopForm(key));
    }

    /**
     * 删除问卷到回收站
     */
    @Log(title = "智能问卷", businessType = BusinessType.UPDATE)
    @GetMapping("logic/delete/{key}")
    public R<Void> logicDeleteForm(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return toAjax(formService.logicDeleteForm(key));
    }

    /**
     * 删除
     */
    @Log(title = "智能问卷", businessType = BusinessType.DELETE)
    @GetMapping("delete/{key}")
    public R<Void> deleteForm(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return toAjax(formService.deleteForm(key));
    }

    /**
     * 查询问卷详情
     * 包含问卷信息 表单字段信息 表单主题
     *
     * @param key 问卷key
     */
    @GetMapping("details/{key}")
    public R<UserFormDetailVO> queryFormDetails(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return formService.queryFormDetails(key);
    }

    /**
     * 问卷表单项查询
     */
    @GetMapping("item/list")
    public R<List<UserFormItemEntity>> queryFormItems(QueryFormItemRequest request) {
        return R.ok(formItemService.queryFormItems(request));
    }

    /**
     * 批量创建问卷表单项
     */
    @Log(title = "智能问卷-问卷组件", businessType = BusinessType.INSERT)
    @PostMapping("item/batch/create")
    public R<Void> batchCreateFormItem(@RequestBody List<UserFormItemEntity> itemEntityList) {
        return toAjax(formItemService.batchCreateFormItem(itemEntityList));
    }

    /**
     * 问卷表单项更新
     *
     * @param request 表单项信息
     */
    @Log(title = "智能问卷-问卷组件", businessType = BusinessType.UPDATE)
    @PostMapping("item/update")
    public R<Void> updateFormItem(@RequestBody UserFormItemEntity request) {
        return toAjax(formItemService.updateFormItem(request));
    }

    /**
     * 问卷表单项删除
     */
    @Log(title = "智能问卷-问卷组件", businessType = BusinessType.DELETE)
    @GetMapping("item/delete/{key}")
    public R<Void> deleteFormItem(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return toAjax(formItemService.deleteFormItem(key));
    }

    /**
     * 问卷表单项排序
     *
     * @param request 表单项信息
     */
    @PostMapping("item/sort")
    public R<OperateFormItemVO> sortFormItem(@RequestBody SortFormItemRequest request) {
        return formItemService.sortFormItem(request) == null ? R.fail("排序失败") : R.ok();
    }

    /**
     * 保存表单逻辑
     *
     * @param userFormLogicEntity 表单逻辑
     * @return 表单逻辑
     */
    @PostMapping("logic/save")
    public R<Void> saveUserFormLogic(@RequestBody UserFormLogicEntity userFormLogicEntity) {
        return toAjax(userFormLogicService.saveUserFormLogin(userFormLogicEntity));
    }

    /**
     * 查询表单逻辑
     *
     * @param key 表单key
     * @return 表单逻辑
     */
    @GetMapping("/user/form/logic/{key}")
    public R<UserFormLogicEntity> queryFormLogic(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return R.ok(userFormLogicService.queryFormLogic(key));
    }

    /**
     * H5端查询问卷表单项
     */
    @SaIgnore
    @GetMapping("h5/form/{key}")
    public R<UserFormDetailVO> queryFormToH5(@PathVariable @NotBlank(message = "问卷key不能为空") String key) {
        return formService.queryFormDetails(key);
    }

}
