package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报名状态
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ApplyStatusEnum implements IDictEnum<Integer> {
    NOT_APPLY(0, "未报名"),
    AUDITING(1, "待审核"),
    APPLYED(2, "已报名"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static ApplyStatusEnum findByValue(Integer value) {
        for (ApplyStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static ApplyStatusEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
