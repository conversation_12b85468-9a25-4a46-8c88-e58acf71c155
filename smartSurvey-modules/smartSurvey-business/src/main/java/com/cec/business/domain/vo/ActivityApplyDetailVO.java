package com.cec.business.domain.vo;

import com.cec.business.domain.enums.ActivityStatusEnum;
import com.cec.business.domain.enums.ApplyStatusEnum;
import com.cec.business.domain.enums.ApplyedStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动主视图对象 activity
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class ActivityApplyDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 自定义富文本
     */
    private String richDescription;

    /**
     * 封面图
     */
    private String coverImg;

    /**
     * 活动状态
     */
    private ActivityStatusEnum status;

    /**
     * 开启报名(1-是;0-否)
     */
    private Boolean applyEnabled;

    /**
     * 报名开始时间
     */
    private Date applyStartTime;

    /**
     * 报名结束时间
     */
    private Date applyEndTime;

    /***
     * 报名状态
     */
    private ApplyedStatusEnum applyStatus = ApplyedStatusEnum.CLOSE;

    /***
     * 已报名的时间
     */
    private Date applyedTime;

}


