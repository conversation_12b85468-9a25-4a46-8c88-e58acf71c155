package com.cec.business.domain.vo;

import com.cec.business.domain.bo.UserFormDataDetailEntity;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * 答题详细信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@FieldNameConstants
@AutoMapper(target = UserFormDataDetailEntity.class)
public class UserFormDataDetailVO extends BaseEntity {

    private Long id;
    /**
     * 表单key
     */
    @NotBlank(message = "错误请求")
    private String formKey;

    /**
     * 回答id
     */
    private Long dataId;

    /***
     * 表单项id
     */
    private String formItemId;

    /**
     * 答题类型
     */
    private String itemType;

    /**
     * 答题值
     */
    private String answerValue;

}
