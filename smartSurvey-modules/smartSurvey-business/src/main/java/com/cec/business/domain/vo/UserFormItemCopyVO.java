package com.cec.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.bo.UserFormItemEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 表单表单项(FormItem)表实体类
 */
@Data
@FieldNameConstants
@AutoMapper(target = UserFormItemEntity.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFormItemCopyVO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 表单项类型
     */
    private String type;

    /**
     * 表单项标题
     */
    private String label;

    /**
     * 表单key
     */
    private String formKey;

    /**
     * 组件id
     */
    private String formItemId;

    /**
     * 展示类型组件 只在表单填写页查询到
     */
    private Boolean displayType;

    /**
     * 隐藏类型组件 在表单填写页面无法查看到
     */
    private Boolean hideType;

    /**
     * 需要在入库前特殊处理的组件 比如随机编码等 验重
     */
    private Boolean specialType;

    /**
     * 是否显示标签
     */
    private Boolean showLabel;

    /**
     * 表单项默认值
     */
    private String defaultValue;

    /**
     * 是否必填
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean required;
    /**
     * 输入型提示文字
     */
    private String placeholder;
    /**
     * 排序
     */
    private Long sort;

    /**
     * 栅格宽度
     */
    private int span;

    /**
     * 扩展字段 表单项独有字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> scheme;

    /**
     * 正则表达式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> regList;

}
