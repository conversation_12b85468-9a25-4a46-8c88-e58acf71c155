package com.cec.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.domain.bo.UserFormLogicEntity;
import com.cec.business.mapper.UserFormLogicMapper;
import com.cec.business.service.UserFormLogicService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserFormLogicServiceImpl extends ServiceImpl<UserFormLogicMapper, UserFormLogicEntity> implements UserFormLogicService {
    private final UserFormLogicMapper userFormLogicMapper;

    @Override
    public Boolean saveUserFormLogin(UserFormLogicEntity userFormLogicEntity) {
        UserFormLogicEntity formLogic = userFormLogicMapper.selectOne(Wrappers.<UserFormLogicEntity>lambdaQuery()
            .eq(UserFormLogicEntity::getFormKey, userFormLogicEntity.getFormKey()));
        if (ObjectUtil.isNotNull(formLogic)) {
            userFormLogicEntity.setId(formLogic.getId());
            userFormLogicMapper.updateById(userFormLogicEntity);
        } else {
            userFormLogicMapper.insert(userFormLogicEntity);
        }
        return true;
    }

    @Override
    public UserFormLogicEntity queryFormLogic(String formKey) {
        return userFormLogicMapper.selectOne(Wrappers.<UserFormLogicEntity>lambdaQuery()
            .eq(UserFormLogicEntity::getFormKey, formKey));
    }
}
