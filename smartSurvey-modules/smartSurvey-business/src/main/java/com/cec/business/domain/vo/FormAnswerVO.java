package com.cec.business.domain.vo;

import com.cec.business.domain.enums.FormJumpTypeEnum;
import lombok.Data;

import java.io.Serializable;

/***
 * 表单回答后返回给前端的内容
 */
@Data
public class FormAnswerVO implements Serializable {

    /***
     * 表单key
     */
    private String formKey;

    /***
     * 是否答题成功
     */
    private Boolean success = false;

    /**
     * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
     */
    private FormJumpTypeEnum jumpType;

    /**
     * 跳转内容
     */
    private String jumpContent;

    /**
     * 指定页面路径
     */
    private String jumpUrl;

}
