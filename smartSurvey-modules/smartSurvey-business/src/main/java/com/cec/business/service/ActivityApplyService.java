package com.cec.business.service;

import com.cec.business.domain.bo.ActivityApplyEntity;
import com.cec.business.domain.req.ActivityApplyAuditRequest;
import com.cec.business.domain.req.ActivityApplyRequest;
import com.cec.business.domain.req.JoinActivityRequest;
import com.cec.business.domain.vo.ActivityApplyVO;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

public interface ActivityApplyService {

    /**
     * 查询活动报名记录
     *
     * @param id 主键
     * @return 活动报名记录
     */
    ActivityApplyVO queryById(Long id);

    /**
     * 分页查询活动报名记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动报名记录分页列表
     */
    TableDataInfo<ActivityApplyVO> queryPageList(ActivityApplyRequest bo, PageQuery pageQuery);

    /**
     * 修改活动报名记录
     *
     * @param bo 活动报名记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ActivityApplyEntity bo);

    /***
     * 活动报名审核
     * @param request
     */
    Boolean audit(ActivityApplyAuditRequest request);

    /***
     * 参与活动
     * @param param 参与活动的参数
     */
    Boolean joinApply(JoinActivityRequest param);

    /***
     * 签到
     * @param param 签到的参数
     */
    Boolean joinSignin(JoinActivityRequest param);
}
