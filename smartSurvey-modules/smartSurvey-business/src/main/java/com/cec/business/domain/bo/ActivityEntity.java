package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.business.domain.enums.ActivityStatusEnum;
import com.cec.business.domain.enums.FormCollectLimitTypeEnum;
import com.cec.business.domain.enums.FormJumpTypeEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.util.Date;

/**
 * 活动主业务对象 activity
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity")
public class ActivityEntity extends BaseEntity {

    /**
     * 活动ID
     */
    @NotNull(message = "{common.id.not.blank}", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动名称
     */
    @NotBlank(message = "{activity.form.name.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 自定义富文本
     */
    private String richDescription;

    /**
     * 封面图
     */
    @NotBlank(message = "{activity.form.coverImg.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String coverImg;

    /**
     * 开启报名(1-是;0-否)
     */
    @TableField(value = "apply_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean applyEnabled;

    /**
     * 报名类型（0-所有人;1-指定人）
     */
    private FormCollectLimitTypeEnum applyLimitType;

    /**
     * 报名开始时间
     */
    private Date applyStartTime;

    /**
     * 报名结束时间
     */
    private Date applyEndTime;

    /**
     * 报名审核(1-是;0-否)
     */
    @TableField(value = "apply_audit_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean applyAuditEnabled;

    /**
     * 设定报名数量上限(1-是;0-否)
     */
    @TableField(value = "apply_limit_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean applyLimitEnabled;

    /**
     * 设定报名数量上限(0-无限制)
     */
    private Integer applyLimit;

    /***
     * 报名可签到(1-是;0-否)
     */
    private Boolean applySigninEnabled;

    /**
     * 开启签到(1-是;0-否)
     */
    @TableField(value = "signin_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean signinEnabled;

    /**
     * 签到开始时间
     */
    private Date signinStartTime;

    /**
     * 签到结束时间
     */
    private Date signinEndTime;

    /**
     * 开启问卷收集(0-关闭;1-开启)
     */
    @TableField(value = "form_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean formEnabled;

    /***
     * 报名可填写
     */
    @TableField(value = "form_join_apply_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean formJoinApplyEnabled;

    /***
     * 签到可填写
     */
    @TableField(value = "form_join_signin_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean formJoinSigninEnabled;

    /**
     * 问卷模板ID
     */
    private Long formTemplateId;

    /**
     * 问卷模板formKey
     */
    private String formTemplateKey;

    /**
     * 问卷表单KEY
     */
    @NotBlank(message = "{activity.form.formKey.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String formKey;

    /**
     * 表单标题
     */
    private String formTitle;

    /**
     * 表单标题描述
     */
    private String formDescription;


    /**
     * 问卷开始时间
     */
    private Date formStartTime;

    /**
     * 问卷结束时间
     */
    private Date formEndTime;

    /**
     * 活动状态(0-草稿;1-进行中;2-已结束;3-已关闭)
     */
    private ActivityStatusEnum status;

    /**
     * 是否可预览答卷(1-是；0-否)
     */
    @TableField(value = "preview_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean previewEnabled;

    /**
     * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
     */
    private FormJumpTypeEnum jumpType;

    /**
     * 跳转内容
     */
    private String jumpContent;

    /**
     * 指定页面路径
     */
    private String jumpUrl;

    /***
     * 签到url
     */
    private String signinUrl;

    /***
     * 报名url
     */
    private String applyUrl;

    /***
     * 问卷url
     */
    private String formUrl;

    /***
     * 是否删除(0-未删除;1-已删除)
     */
    @TableField(value = "is_deleted", typeHandler = BooleanTypeHandler.class)
    private Boolean deleted;

}
