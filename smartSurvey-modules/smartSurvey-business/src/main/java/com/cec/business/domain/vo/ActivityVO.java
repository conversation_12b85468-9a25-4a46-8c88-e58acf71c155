package com.cec.business.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.cec.business.domain.bo.ActivityEntity;
import com.cec.business.domain.enums.ActivityFormJoinTypeEnum;
import com.cec.business.domain.enums.ActivityStatusEnum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动主视图对象 activity
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActivityEntity.class)
public class ActivityVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 封面图
     */
    private String coverImg;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 是否开启报名(0-关闭;1-开启)
     */
    private Boolean applyEnabled;

    /**
     * 报名开始时间
     */
    private Date applyStartTime;

    /**
     * 报名结束时间
     */
    private Date applyEndTime;

    /**
     * 是否开启审核(0-关闭;1-开启)
     */
    private Boolean applyAuditEnabled;

    /**
     * 报名人数限制(0-无限制)
     */
    private Integer applyLimit;

    /**
     * 是否开启签到(0-关闭;1-开启)
     */
    private Boolean signinEnabled;

    /**
     * 签到开始时间
     */
    private Date signinStartTime;

    /**
     * 签到结束时间
     */
    private Date signinEndTime;

    /**
     * 是否开启问卷(0-关闭;1-开启)
     */
    private Boolean formEnabled;

    /**
     * 问卷类型(1-报名后;2-签到后)
     */
    private ActivityFormJoinTypeEnum formJoinType;

    /**
     * 问卷模板ID
     */
    private Long formTemplateId;

    /**
     * 问卷模板formKey
     */
    private String formTemplateKey;

    /**
     * 表单标题
     */
    private String formTitle;

    /**
     * 表单标题描述
     */
    private String formDescription;

    /**
     * 问卷表单KEY
     */
    private String formKey;

    /**
     * 问卷开始时间
     */
    private Date formStartTime;

    /**
     * 问卷结束时间
     */
    private Date formEndTime;

    /**
     * 活动状态
     */
    private ActivityStatusEnum status;

    /***
     * 创建人姓名
     */
    private String createByName;

    /***
     * 修改人姓名
     */
    private String updateByName;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新者
     */
    private Long updateBy;

    /***
     * 创建时间
     */
    private Date createTime;

    /***
     * 修改时间
     */
    private Date updateTime;

    /***
     * 签到url
     */
    private String signinUrl;

    /***
     * 报名url
     */
    private String applyUrl;

    /***
     * 问卷url
     */
    private String formUrl;

    /**
     * 角色key
     */
    private String roleKey;

    /**
     * 活动管理员IDs
     */
    private Long[] activityManagerUserIds;

    /**
     * 活动成员IDs
     */
    private Long[] activityMemberUserIds;

    /**
     * 活动成员部门IDs
     */
    private Long[] activityDeptIds;

    /***
     * 指定名单(用户ID列表)
     */
    private List<Long> userIdList;

    /***
     * 指定名单（部门ID列表）
     */
    private List<Long> deptIdList;

    /***
     * 为false表示未创建过问卷组件，true表示已创建过问卷组件
     */
    private Boolean created = false;

}
