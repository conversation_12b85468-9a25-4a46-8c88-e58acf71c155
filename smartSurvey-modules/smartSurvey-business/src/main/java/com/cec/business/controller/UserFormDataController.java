package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.cec.business.domain.req.UserFormDataAnswerRequest;
import com.cec.business.domain.req.UserFormDataSearchRequest;
import com.cec.business.domain.vo.FormAnswerVO;
import com.cec.business.domain.vo.FormDataStatDateVO;
import com.cec.business.domain.vo.FormDataStatFileVO;
import com.cec.business.domain.vo.FormDataStatSelectVO;
import com.cec.business.domain.vo.FormDataStatTextVO;
import com.cec.business.domain.vo.FormDataStatisticsVO;
import com.cec.business.domain.vo.FormDataTableVO;
import com.cec.business.service.UserFormDataService;
import com.cec.common.core.domain.R;
import com.cec.common.core.utils.HttpUtils;
import com.cec.common.core.validate.ValidatorUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收集表单的回答数据
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/form/data/")
@SaIgnore
public class UserFormDataController extends BaseController {
    private final UserFormDataService userFormDataService;

    /**
     * 回答题目
     *
     * @param req     填写数据
     * @param request 请求
     */
    @PostMapping("answer")
    public R<FormAnswerVO> answerUserForm(@RequestBody UserFormDataAnswerRequest req, HttpServletRequest request) {
        ValidatorUtils.validateEntity(req);
        req.setSubmitRequestIp(HttpUtils.getIpAddr(request));
        req.setSubmitBrowser(HttpUtils.getBrowser(request));
        return R.ok(userFormDataService.answerUserForm(req));
    }

    /***
     * 问卷的数据详情
     */
    @GetMapping("detail")
    public R<FormDataTableVO> answerUserFormDetail(UserFormDataSearchRequest req) {
        return R.ok(userFormDataService.answerUserFormDetail(req));
    }

    /**
     * 问卷的数据统计概览
     *
     * @param formKey 问卷formKey
     */
    @GetMapping("stat/{formKey}")
    public R<FormDataStatisticsVO> answerStatistics(@PathVariable @NotBlank(message = "问卷id不能为空") String formKey) {
        return R.ok(userFormDataService.answerStatistics(formKey));
    }

    /**
     * 问卷的文本数据统计，适用于text-input/textarea/number-input
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     * @param pageQuery  分页参数
     */
    @GetMapping("stat/text/{formItemId}")
    public R<FormDataStatTextVO> answerStatisticsText(
            @PathVariable @NotBlank(message = "问卷组件id不能为空") String formItemId,
            @RequestParam @NotBlank(message = "问卷Key不能为空") String formKey,
            PageQuery pageQuery) {
        return R.ok(userFormDataService.answerStatisticsText(formItemId, formKey, pageQuery));
    }

    /**
     * 问卷的单选/多选数据统计，适用于select/radio-group/checkbox-group/NSP/rating/matrix-radio
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     */
    @GetMapping("stat/select/{formItemId}")
    public R<FormDataStatSelectVO> answerStatisticsSelect(
            @PathVariable @NotBlank(message = "问卷组件id不能为空") String formItemId,
            @RequestParam @NotBlank(message = "问卷Key不能为空") String formKey) {
        return R.ok(userFormDataService.answerStatisticsSelect(formItemId, formKey));
    }

    /**
     * 问卷的日期数据统计，适用于date-picker
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     * @param statType 统计类型（year-年度统计，month-按月统计，day-按日统计）
     */
    @GetMapping("stat/date/{formItemId}")
    public R<FormDataStatDateVO> answerStatisticsDate(
            @PathVariable @NotBlank(message = "问卷组件id不能为空") String formItemId,
            @RequestParam @NotBlank(message = "问卷Key不能为空") String formKey,
            @RequestParam(defaultValue = "day") String statType) {
        return R.ok(userFormDataService.answerStatisticsDate(formItemId, formKey, statType));
    }

    /**
     * 问卷的文件数据统计，适用于upload
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     */
    @GetMapping("stat/file/{formItemId}")
    public R<FormDataStatFileVO> answerStatisticsFile(
            @PathVariable @NotBlank(message = "问卷组件id不能为空") String formItemId,
            @RequestParam @NotBlank(message = "问卷Key不能为空") String formKey,
            PageQuery pageQuery) {
        return R.ok(userFormDataService.answerStatisticsFile(formItemId, formKey, pageQuery));
    }

    /**
     * 下载表单项的所有附件为压缩包
     *
     * @param formItemId 表单项ID
     * @param formKey 问卷Key
     */
    @GetMapping("download/files/{formItemId}")
    public void downloadAllFiles(
            @PathVariable @NotBlank(message = "问卷组件id不能为空") String formItemId,
            @RequestParam @NotBlank(message = "问卷Key不能为空") String formKey,
            HttpServletResponse response) throws Exception {
        userFormDataService.downloadAllFiles(formItemId, formKey, response);
    }
}
