package com.cec.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.domain.bo.UserFormItemEntity;
import com.cec.business.domain.req.QueryFormItemRequest;
import com.cec.business.domain.req.SortFormItemRequest;
import com.cec.business.domain.vo.FormFieldVO;
import com.cec.business.domain.vo.OperateFormItemVO;
import com.cec.business.mapper.UserFormItemMapper;
import com.cec.business.mapper.UserFormThemeMapper;
import com.cec.business.service.UserFormItemService;
import com.cec.business.utils.SortUtils;
import com.cec.common.core.domain.R;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.validate.UpdateGroup;
import com.cec.common.core.validate.ValidatorUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/***
 * 表单表单项(FormItem)表服务实现类
 */
@Service
public class UserFormItemServiceImpl extends ServiceImpl<UserFormItemMapper, UserFormItemEntity> implements UserFormItemService {
    private final UserFormThemeMapper userFormThemeMapper;
    private final SortUtils sortUtils;

    @Autowired  // 添加构造函数注入
    public UserFormItemServiceImpl(UserFormThemeMapper userFormThemeMapper,
                                   SortUtils sortUtils) {
        this.userFormThemeMapper = userFormThemeMapper;
        this.sortUtils = sortUtils;
    }

    @Override
    public List<UserFormItemEntity> listByFormKey(String key) {
        List<UserFormItemEntity> list = this.list(Wrappers.<UserFormItemEntity>lambdaQuery().eq(UserFormItemEntity::getFormKey, key));
        list.sort(Comparator.comparing(UserFormItemEntity::getSort));
        return list;
    }

    @Override
    public List<FormFieldVO> listFormFields(String formKey) {
        List<UserFormItemEntity> itemEntityList = this.list(Wrappers.<UserFormItemEntity>lambdaQuery().eq(UserFormItemEntity::getFormKey, formKey).eq(UserFormItemEntity::getDisplayType, 0));
        itemEntityList.sort(Comparator.comparing(UserFormItemEntity::getSort));
        // FormFieldVO 处理了部份组价默认显示label字段
        return itemEntityList.stream().map(FormFieldVO::new).collect(Collectors.toList());
    }


    @Override
    public Long getLastItemSort(String formKey) {
        Page<UserFormItemEntity> formItemEntityPage = baseMapper.selectPage(new Page<>(1, 1, false),
            Wrappers.<UserFormItemEntity>lambdaQuery().eq(UserFormItemEntity::getFormKey, formKey)
                .orderByDesc(UserFormItemEntity::getSort));
        // 去取第一个元素
        UserFormItemEntity first = CollUtil.getFirst(formItemEntityPage.getRecords());
        return ObjectUtil.isNull(first) ? 0 : first.getSort();
    }

    @Override
    public Boolean isSpecialTypeItem(UserFormItemEntity userFormItemEntity) {
        // 随机编号
//        if (userFormItemEntity.getType() == FormItemTypeEnum.RANDOM_NUMBER) {
//            return true;
//        }
//        // 不允许重复
//        if (userFormItemEntity.getType() == FormItemTypeEnum.INPUT) {
//            InputResultStruct builder = InputResultStruct.builder(userFormItemEntity.getScheme());
//            return builder.isNotRepeat();
//        }
//        // 商品
//        if (userFormItemEntity.getType() == FormItemTypeEnum.GOODS_SELECT) {
//            return true;
//        }
//        // 预约时间
//        if (userFormItemEntity.getType() == FormItemTypeEnum.RESERVE_DAY || userFormItemEntity.getType() == FormItemTypeEnum.RESERVE_TIME_RANGE) {
//            return true;
//        }
//        // 投票
//        if (userFormItemEntity.getType() == FormItemTypeEnum.CHECKBOX || userFormItemEntity.getType() == FormItemTypeEnum.RADIO || userFormItemEntity.getType() == FormItemTypeEnum.IMAGE_SELECT) {
//            CheckboxSchemaStruct builder = CheckboxSchemaStruct.builder(userFormItemEntity.getScheme());
//            // 单选多选带名额
//            if (builder.getConfig().getOptions().stream().anyMatch(item -> ObjectUtil.isNotNull(item.getQuotaSetting()))) {
//                return true;
//            }
//            return builder.getConfig().isShowVoteResult();
//        }
        return false;
    }

    @Override
    public List<UserFormItemEntity> queryFormItems(QueryFormItemRequest request) {
        ValidatorUtils.validateEntity(request);
        List<UserFormItemEntity> itemEntityList = list(Wrappers.<UserFormItemEntity>lambdaQuery()
            .ne(UserFormItemEntity::getHideType, 1)
            .eq(UserFormItemEntity::getFormKey, request.getKey()));
        itemEntityList.sort(Comparator.comparing(UserFormItemEntity::getSort));
        return itemEntityList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateFormItem(List<UserFormItemEntity> itemEntityList) {
        if (CollUtil.isEmpty(itemEntityList)) {
            throw new ServiceException(MessageUtils.message("form.data.empty"));
        }
        // 删除所有表单项
        remove(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormKey, itemEntityList.get(0).getFormKey()));
        // 排序下标计算
//        itemEntityList.forEach(item -> item.setSort(sortUtils.getInitialSortPosition(item.getFormKey())));
        return saveBatch(itemEntityList);
    }

    @Override
    public Boolean updateFormItem(UserFormItemEntity request) {
        ValidatorUtils.validateEntity(request, UpdateGroup.class);
        return update(request, Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormKey, request.getFormKey())
            .eq(UserFormItemEntity::getFormItemId, request.getFormItemId()));
    }

    @Override
    public Boolean deleteFormItem(String key) {
        return remove(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormKey, key));
    }

    @Override
    public OperateFormItemVO sortFormItem(SortFormItemRequest request) {
        ValidatorUtils.validateEntity(request);
        if (ObjectUtil.isNull(request.getAfterPosition()) && ObjectUtil.isNull(request.getBeforePosition())) {
            return null;
        }
        UserFormItemEntity itemEntity = getOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormKey, request.getFormKey())
            .eq(UserFormItemEntity::getFormItemId, request.getFormItemId()));
        Long sort = sortUtils.calcSortPosition(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey());
        if (sortUtils.sortAllList(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey(), sort)) {
            return new OperateFormItemVO(itemEntity.getSort(), itemEntity.getId(), true, true);
        }
        itemEntity.setSort(sort);
        boolean b = updateById(itemEntity);
        return new OperateFormItemVO(itemEntity.getSort(), itemEntity.getId(), b, false);
    }

}
