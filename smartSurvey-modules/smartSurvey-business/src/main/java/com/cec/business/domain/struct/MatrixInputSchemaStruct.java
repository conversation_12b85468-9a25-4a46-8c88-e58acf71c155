package com.cec.business.domain.struct;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description : 矩阵输入组件结构
 * @create :  2021/06/07 16:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MatrixInputSchemaStruct {


    private Table table;

    @Data
    public static class Table {
        private List<Item> rows;
        private List<Item> columns;
    }


    @Data
    public static class Item {
        private String label;
        private String id;
    }
}
