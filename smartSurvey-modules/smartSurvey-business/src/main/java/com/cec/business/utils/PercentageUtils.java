package com.cec.business.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 百分比工具类
 * 提供各种百分比格式化方法
 */
public class PercentageUtils {

    /**
     * 默认百分比格式化器（保留1位小数）
     */
    private static final DecimalFormat DEFAULT_PERCENT_FORMAT = new DecimalFormat("0.0%");
    
    /**
     * 整数百分比格式化器（不保留小数）
     */
    private static final DecimalFormat INTEGER_PERCENT_FORMAT = new DecimalFormat("0%");

    /**
     * 高精度百分比格式化器（保留2位小数）
     */
    private static final DecimalFormat PRECISE_PERCENT_FORMAT = new DecimalFormat("0.00%");

    /**
     * 计算百分比并格式化为字符串（保留1位小数）
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @return 格式化的百分比字符串，如 "85.5%"
     */
    public static String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return DEFAULT_PERCENT_FORMAT.format(ratio);
    }

    /**
     * 计算百分比并格式化为字符串（保留1位小数）
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @return 格式化的百分比字符串，如 "85.5%"
     */
    public static String formatPercentage(double numerator, double denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = numerator / denominator;
        return DEFAULT_PERCENT_FORMAT.format(ratio);
    }

    /**
     * 格式化已计算好的比率为百分比字符串（保留1位小数）
     * 
     * @param ratio 比率值（0-1之间）
     * @return 格式化的百分比字符串，如 "85.5%"
     */
    public static String formatRatio(double ratio) {
        return DEFAULT_PERCENT_FORMAT.format(ratio);
    }

    /**
     * 计算百分比并格式化为整数百分比字符串
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @return 格式化的百分比字符串，如 "86%"
     */
    public static String formatPercentageAsInteger(long numerator, long denominator) {
        if (denominator == 0) {
            return "0%";
        }
        double ratio = (double) numerator / denominator;
        return INTEGER_PERCENT_FORMAT.format(ratio);
    }

    /**
     * 计算百分比并格式化为高精度百分比字符串（保留2位小数）
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @return 格式化的百分比字符串，如 "85.50%"
     */
    public static String formatPercentagePrecise(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.00%";
        }
        double ratio = (double) numerator / denominator;
        return PRECISE_PERCENT_FORMAT.format(ratio);
    }

    /**
     * 使用BigDecimal进行高精度百分比计算和格式化
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @param scale       小数位数
     * @return 格式化的百分比字符串
     */
    public static String formatPercentageWithBigDecimal(long numerator, long denominator, int scale) {
        if (denominator == 0) {
            return "0%";
        }
        
        BigDecimal num = BigDecimal.valueOf(numerator);
        BigDecimal den = BigDecimal.valueOf(denominator);
        BigDecimal ratio = num.divide(den, scale + 2, RoundingMode.HALF_UP);
        BigDecimal percentage = ratio.multiply(BigDecimal.valueOf(100));
        
        return percentage.setScale(scale, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 根据本地化设置格式化百分比
     * 
     * @param ratio  比率值（0-1之间）
     * @param locale 本地化设置
     * @return 格式化的百分比字符串
     */
    public static String formatPercentageLocalized(double ratio, Locale locale) {
        NumberFormat percentFormat = NumberFormat.getPercentInstance(locale);
        percentFormat.setMinimumFractionDigits(1);
        percentFormat.setMaximumFractionDigits(1);
        return percentFormat.format(ratio);
    }

    /**
     * 智能格式化百分比：
     * - 如果是整数百分比，不显示小数点
     * - 如果有小数，显示1位小数
     * 
     * @param numerator   分子
     * @param denominator 分母
     * @return 格式化的百分比字符串
     */
    public static String formatPercentageSmart(long numerator, long denominator) {
        if (denominator == 0) {
            return "0%";
        }
        
        double ratio = (double) numerator / denominator;
        double percentage = ratio * 100;
        
        // 如果是整数百分比，不显示小数点
        if (percentage == Math.floor(percentage)) {
            return INTEGER_PERCENT_FORMAT.format(ratio);
        } else {
            return DEFAULT_PERCENT_FORMAT.format(ratio);
        }
    }
}
