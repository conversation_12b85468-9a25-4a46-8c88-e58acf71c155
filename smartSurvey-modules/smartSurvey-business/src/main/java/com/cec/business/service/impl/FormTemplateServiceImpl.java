package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.domain.bo.*;
import com.cec.business.domain.enums.FormSourceTypeEnum;
import com.cec.business.domain.req.QueryFormTemplateRequest;
import com.cec.business.domain.vo.FormTemplateVO;
import com.cec.business.domain.vo.UserFormCopyVO;
import com.cec.business.domain.vo.UserFormItemCopyVO;
import com.cec.business.mapper.FormTemplateMapper;
import com.cec.business.service.*;
import com.cec.business.utils.ShortIdUtils;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 表单表(FormTemplate)表服务实现类
 */
@Service
@RequiredArgsConstructor
public class FormTemplateServiceImpl extends ServiceImpl<FormTemplateMapper, FormTemplateEntity> implements FormTemplateService {
    private final UserFormService userFormService;
    private final UserFormItemService userFormItemService;
    private final UserFormThemeService userFormThemeService;
    private final UserFormLogicService userFormLogicService;
    private final ISysUserService sysUserService;

    @Override
    public TableDataInfo<FormTemplateVO> selectFormTemplatePageList(QueryFormTemplateRequest request, PageQuery pageQuery) {
        Page<FormTemplateVO> page = (Page<FormTemplateVO>) baseMapper.selectVoPage(pageQuery.build(), Wrappers.<FormTemplateEntity>lambdaQuery()
            .like(ObjectUtil.isNotNull(request.getName()), FormTemplateEntity::getName, request.getName())
            .orderByDesc(FormTemplateEntity::getCreateTime)
        ).convert(item -> {
            item.setCreateByName(sysUserService.selectUserById(item.getCreateBy()).getUserName());
            item.setHasDelete(item.getCreateBy().equals(LoginHelper.getUserId()));
            return item;
        });
        return TableDataInfo.build(page);
    }

    @Override
    public void deleteByKey(String key) {
        FormTemplateEntity templateEntity = getByKey(key);
        Assert.notNull(templateEntity, () -> new ServiceException(MessageUtils.message("form.not.exists")));
        Assert.isTrue(templateEntity.getCreateBy().equals(LoginHelper.getUserId()),
            () -> new ServiceException(MessageUtils.message("form.no.permission")));
        remove(Wrappers.<FormTemplateEntity>lambdaQuery().eq(FormTemplateEntity::getFormKey, key));
    }

    /***
     * 获取模板定义
     * @param key   模版key
     * @return 模板定义
     */
    @Override
    public FormTemplateEntity.Definition getTemplateDefinition(String key) {
        FormTemplateEntity templateEntity = getByKey(key);
        if (templateEntity == null) {
            return null;
        }
        if (ObjectUtil.isNull(templateEntity.getScheme())) {
            return null;
        }
        return templateEntity.getScheme();
    }

    @Override
    public FormTemplateEntity getByKey(String key) {
        return this.getOne(Wrappers.<FormTemplateEntity>lambdaQuery()
            .eq(FormTemplateEntity::getFormKey, key));
    }

    @Override
    public FormTemplateEntity createFormTemplate(FormTemplateEntity formTemplateEntity) {
        if (ObjectUtil.isEmpty(formTemplateEntity.getFormKey())) {
            throw new ServiceException(MessageUtils.message("form.not.exists"));
        }
        // 保存基础信息和问题
        String formKey = formTemplateEntity.getFormKey();
        formTemplateEntity.setId(IdWorker.getId());
        formTemplateEntity.setFormKey(ShortIdUtils.genId());
        formTemplateEntity.setSourceFormKey(formKey);
        formTemplateEntity.setCreateBy(LoginHelper.getUserId());
        formTemplateEntity.setCreateTime(DateUtil.date());
        formTemplateEntity.setScheme(buildDefinition(formKey));
        save(formTemplateEntity);
        return formTemplateEntity;
    }

    private FormTemplateEntity.Definition buildDefinition(String formKey) {
        // 查询表单
        UserFormEntity userFormEntity = userFormService.getByKey(formKey);
        Assert.notNull(userFormEntity, () -> new ServiceException(MessageUtils.message("form.not.exists")));
        // 查询表单的Item列表
        List<UserFormItemCopyVO> itemEntityList = handlerUserFormItemCopyVO(formKey);
        // 主题
        UserFormThemeEntity themeEntity = userFormThemeService.getByKey(formKey);
        FormTemplateEntity.Definition definition = new FormTemplateEntity.Definition();
        definition.setFormType(userFormEntity.getType().getValue());
        definition.setFormItems(itemEntityList);
        definition.setUserFormTheme(themeEntity);
        definition.setUserFormLogic(userFormLogicService.getOne(Wrappers.<UserFormLogicEntity>lambdaQuery()
            .eq(UserFormLogicEntity::getFormKey, formKey)));
        definition.setUserForm(handlerUserFormCopyVO(userFormEntity));
        return definition;
    }

    private List<UserFormItemCopyVO> handlerUserFormItemCopyVO(String formKey) {
        List<UserFormItemEntity> itemEntityList = userFormItemService.listByFormKey(formKey);
        List<UserFormItemCopyVO> itemEntityCopyList = new ArrayList<>(itemEntityList.size());
        // 将itemEntityList数据放到itemEntityCopyList中
        for (UserFormItemEntity itemEntity : itemEntityList) {
            UserFormItemCopyVO copyVO = new UserFormItemCopyVO();
            BeanUtils.copyProperties(itemEntity, copyVO);
            itemEntityCopyList.add(copyVO);
        }
        return itemEntityCopyList;
    }

    private UserFormCopyVO handlerUserFormCopyVO(UserFormEntity userForm) {
        Long sourceId = userForm.getId();
        UserFormCopyVO userFormCopyVO = new UserFormCopyVO();
        BeanUtil.copyProperties(userForm, userFormCopyVO, "id", "formKey", "status");
        userFormCopyVO.setCollectLimitType(userForm.getCollectLimitType().getValue());
        userFormCopyVO.setSourceType(userForm.getSourceType().getValue());
        userFormCopyVO.setJumpType(userForm.getJumpType().getValue());
        userFormCopyVO.setType(userForm.getType().getValue());
        userFormCopyVO.setSourceType(FormSourceTypeEnum.TEMPLATE.getValue());
        userFormCopyVO.setSourceId(String.valueOf(sourceId));
        return userFormCopyVO;
    }

//    @Override
//    public UserFormEntity createFormByTemplate(FormTemplateEntity request) {
//        String templateKey = request.getFormKey();
//        FormTemplateEntity formTemplateEntity = this.getByKey(templateKey);
//        String newFormKey = ShortIdUtils.genId();
//        UserFormEntity userFormEntity = new UserFormEntity();
//        BeanUtil.copyProperties(formTemplateEntity, userFormEntity, UserFormEntity.Fields.status);
//        userFormEntity.setSourceType(FormSourceTypeEnum.TEMPLATE);
//        userFormEntity.setSourceId(formTemplateEntity.getId().toString());
//        userFormEntity.setFormKey(newFormKey);
//        userFormEntity.setStatus(FormStatusEnum.CREATE);
//        if (ObjectUtil.isNotNull(formTemplateEntity.getScheme().getFormType())) {
//            userFormEntity.setType(EnumUtil.getBy(FormTypeEnum::getValue, formTemplateEntity.getScheme().getFormType()));
//        }
//        userFormService.save(userFormEntity);
//        List<UserFormItemCopyVO> formItems = formTemplateEntity.getScheme().getFormItems();
//        formItems.forEach(item -> {
//            item.setFormKey(userFormEntity.getFormKey());
//        });
//        userFormItemService.saveBatch(formItems);
//        // 主题
//        if (ObjectUtil.isNotNull(formTemplateEntity.getScheme().getUserFormTheme())) {
//            UserFormThemeEntity userFormTheme = formTemplateEntity.getScheme().getUserFormTheme();
//            userFormTheme.setFormKey(newFormKey);
//            userFormThemeService.save(userFormTheme);
//        }
//        // 逻辑
//        UserFormLogicEntity userFormLogic = formTemplateEntity.getScheme().getUserFormLogic();
//        if (ObjectUtil.isNotNull(userFormLogic)) {
//            userFormLogic.setId(null);
//            userFormLogic.setFormKey(newFormKey);
//            userFormLogicService.save(userFormLogic);
//        }
//        return userFormEntity;
//    }
}
