package com.cec.business.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 问卷的单选/多选数据统计，适用于select/radio-group/checkbox-group/NSP/rating/matrix-radio
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Accessors(chain = true)
public class FormDataStatSelectVO {



    /**
     * 填写率（带百分号，如：85.5%）
     */
    private String fillRate;

    /**
     * 填写数量
     */
    private Long fillCount;

    /**
     * 数据
     */
    List<FormDataStatSelectItemVO> tableDataInfo;

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class FormDataStatSelectItemVO {

        /**
         * 答案id。没什么用
         */
        private Long id;

        /**
         * 答案值
         */
        private String answerValue;

        /**
         * 题型
         */
        private String itemType;

        /**
         * 填写率（带百分号，如：85.5%）
         */
        private String fillRate;

        /**
         * 填写数量
         */
        private Long fillCount;
    }

}
