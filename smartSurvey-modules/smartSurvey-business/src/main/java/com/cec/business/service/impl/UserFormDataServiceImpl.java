package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.constant.CommonConstants;
import com.cec.business.constant.FormRedisKeyConstants;
import com.cec.business.domain.bo.ActivityApplyEntity;
import com.cec.business.domain.bo.ActivityEntity;
import com.cec.business.domain.bo.ActivitySigninEntity;
import com.cec.business.domain.bo.UserFormAuthEntity;
import com.cec.business.domain.bo.UserFormDataDetailEntity;
import com.cec.business.domain.bo.UserFormDataEntity;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.domain.bo.UserFormItemEntity;
import com.cec.business.domain.enums.ActivityStatusEnum;
import com.cec.business.domain.enums.FormCollectLimitTypeEnum;
import com.cec.business.domain.enums.FormStatusEnum;
import com.cec.business.domain.enums.FormTypeEnum;
import com.cec.business.domain.req.QueryFormItemRequest;
import com.cec.business.domain.req.UserFormDataAnswerRequest;
import com.cec.business.domain.req.UserFormDataSearchRequest;
import com.cec.business.domain.vo.FormAnswerVO;
import com.cec.business.domain.vo.FormDataStatDateVO;
import com.cec.business.domain.vo.FormDataStatFileVO;
import com.cec.business.domain.vo.FormDataStatSelectVO;
import com.cec.business.domain.vo.FormDataStatTextVO;
import com.cec.business.domain.vo.FormDataStatisticsVO;
import com.cec.business.domain.vo.FormDataTableVO;
import com.cec.business.mapper.ActivityApplyMapper;
import com.cec.business.mapper.ActivityMapper;
import com.cec.business.mapper.ActivitySigninMapper;
import com.cec.business.mapper.UserFormAuthMapper;
import com.cec.business.mapper.UserFormDataDetailMapper;
import com.cec.business.mapper.UserFormDataMapper;
import com.cec.business.mapper.UserFormItemMapper;
import com.cec.business.mapper.UserFormMapper;
import com.cec.business.service.UserFormDataService;
import com.cec.business.service.UserFormItemService;
import com.cec.business.service.UserFormService;
import com.cec.business.utils.FormDataUtils;
import com.cec.business.utils.PercentageUtils;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.oss.core.OssClient;
import com.cec.common.oss.factory.OssFactory;
import com.cec.common.redis.utils.CacheUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.vo.SysOssVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.service.ISysOssService;
import com.cec.system.service.ISysUserService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserFormDataServiceImpl extends ServiceImpl<UserFormDataMapper, UserFormDataEntity> implements UserFormDataService {

    private final UserFormService userFormService;
    private final UserFormItemService userformItemService;
    private final UserFormDataDetailMapper userFormDataDetailMapper;
    private final UserFormAuthMapper userFormAuthMapper;
    private final ActivityMapper activityMapper;
    private final ActivityApplyMapper activityApplyMapper;
    private final ActivitySigninMapper activitySigninMapper;
    private final UserFormItemMapper userFormItemMapper;
    private final ISysOssService sysOssService;
    private final FormDataUtils formDataUtils;
    private final CacheUtils redisUtils;
    private final UserFormMapper userFormMapper;
    private final ISysUserService sysUserService;

    /**
     * 表单项类型常量
     */
    private static final String ITEM_TYPE_SELECT = "select";
    private static final String ITEM_TYPE_RADIO_GROUP = "radio-group";
    private static final String ITEM_TYPE_CHECKBOX_GROUP = "checkbox-group";
    private static final String ITEM_TYPE_NSP = "NSP";
    private static final String ITEM_TYPE_RATING = "rating";
    private static final String ITEM_TYPE_TEXT_INPUT = "text-input";
    private static final String ITEM_TYPE_MATRIX_RADIO = "matrix-radio";
    private static final String ITEM_TYPE_TEXTAREA = "textarea";
    private static final String ITEM_TYPE_NUMBER_INPUT = "number-input";

    // 日期类型常量
    private static final String ITEM_TYPE_DATE_PICKER = "date-picker";

    // 文件类型常量
    private static final String ITEM_TYPE_UPLOAD = "upload";

    /**
     * 判断是否为支持的选择类型表单项
     *
     * @param itemType 表单项类型
     * @return 是否为支持的选择类型
     */
    private static boolean isSupportedSelectType(String itemType) {
        return ITEM_TYPE_SELECT.equals(itemType) ||
            ITEM_TYPE_RADIO_GROUP.equals(itemType) ||
            ITEM_TYPE_CHECKBOX_GROUP.equals(itemType) ||
            ITEM_TYPE_NSP.equals(itemType) ||
            ITEM_TYPE_RATING.equals(itemType) ||
            ITEM_TYPE_MATRIX_RADIO.equals(itemType);
    }

    /**
     * 判断是否为单选类型
     *
     * @param itemType 表单项类型
     * @return 是否为单选类型
     */
    private static boolean isSingleSelectType(String itemType) {
        return ITEM_TYPE_SELECT.equals(itemType) ||
            ITEM_TYPE_RADIO_GROUP.equals(itemType) ||
            ITEM_TYPE_NSP.equals(itemType) ||
            ITEM_TYPE_RATING.equals(itemType) ||
            ITEM_TYPE_MATRIX_RADIO.equals(itemType);
    }

    /**
     * 判断是否为多选类型
     *
     * @param itemType 表单项类型
     * @return 是否为多选类型
     */
    private static boolean isMultiSelectType(String itemType) {
        return ITEM_TYPE_CHECKBOX_GROUP.equals(itemType);
    }

    /**
     * 判断是否为文本类型
     *
     * @param itemType 表单项类型
     * @return 是否为文本类型
     */
    private static boolean isTextType(String itemType) {
        return ITEM_TYPE_TEXT_INPUT.equals(itemType) ||
            ITEM_TYPE_TEXTAREA.equals(itemType) ||
            ITEM_TYPE_NUMBER_INPUT.equals(itemType);
    }

    /**
     * 判断是否为日期类型
     *
     * @param itemType 表单项类型
     * @return 是否为日期类型
     */
    private static boolean isDateType(String itemType) {
        return ITEM_TYPE_DATE_PICKER.equals(itemType);
    }

    /**
     * 判断是否为文件类型
     *
     * @param itemType 表单项类型
     * @return 是否为文件类型
     */
    private static boolean isFileType(String itemType) {
        return ITEM_TYPE_UPLOAD.equals(itemType);
    }

    /**
     * 判断答案值是否为空
     * 对于checkbox-group和upload类型，空答案是[]
     * 对于其他类型，空答案是null或空字符串
     *
     * @param answerValue 答案值
     * @return 是否为空答案
     */
    private static boolean isEmptyAnswer(String answerValue) {
        if (StrUtil.isBlank(answerValue)) {
            return true;
        }
        // 过滤空数组 []
        String trimmed = answerValue.trim();
        return "[]".equals(trimmed);
    }

    /***
     * 填写表单
     * @param req    表单数据
     * @return 表单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FormAnswerVO answerUserForm(UserFormDataAnswerRequest req) {
        FormAnswerVO formAnswerVO = null;
        try {
            formAnswerVO = validateRequestParam(req);
            UserFormDataEntity entity = new UserFormDataEntity();
            BeanUtil.copyProperties(req, entity);
            saveUserFormData(entity, formAnswerVO);
        } catch (ServiceException e) {
            log.error("填写表单时发生业务异常", e);
            throw e;
        } catch (Exception e1) {
            // 其他异常，记录日志并抛出，事务会自动回滚
            log.error("填写表单时发生未知异常", e1);
            throw new ServiceException(MessageUtils.message("form.fill.failed"));
        }
        return formAnswerVO;
    }

    private void saveUserFormData(UserFormDataEntity entity, FormAnswerVO formAnswerVO) {
        entity.setId(IdWorker.getId());
        // 提交序号
        long serialNumber = redisUtils.incr(StrUtil.format(FormRedisKeyConstants.FORM_RESULT_NUMBER, entity.getFormKey()), CommonConstants.ConstantNumber.ONE);
        entity.setSerialNumber(serialNumber);
        // 答题人
        entity.setAnswerUserId(LoginHelper.getUserId());
        List<UserFormDataDetailEntity> detailList = new ArrayList<>(entity.getOriginalData().size());
        // 循环答题内容保存到UserFormDataDetai中，过滤空答案
        entity.getOriginalData().forEach((k, v) -> {
            // 处理null值，避免NullPointerException
            String answerValue = v != null ? v.toString() : null;
            // 过滤空答案：null、空字符串、空数组[]
            if (!isEmptyAnswer(answerValue)) {
                UserFormDataDetailEntity detail = new UserFormDataDetailEntity();
                detail.setDataId(entity.getId());
                detail.setFormKey(entity.getFormKey());
                // 通过formItemId查询表单项类型
                UserFormItemEntity item = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
                    .eq(UserFormItemEntity::getFormItemId, k)
                    .eq(UserFormItemEntity::getFormKey, entity.getFormKey()));
                if (item != null) {
                    detail.setItemType(item.getType());
                }
                detail.setFormItemId(k);
                detail.setAnswerValue(answerValue);
                detailList.add(detail);
            }
        });
        save(entity);
        Assert.notNull(detailList, () -> new ServiceException(MessageUtils.message("form.data.empty")));
        userFormDataDetailMapper.insertBatch(detailList);
        formAnswerVO.setSuccess(true);
    }

    private FormAnswerVO validateRequestParam(UserFormDataAnswerRequest req) {
        FormAnswerVO formAnswerVO = new FormAnswerVO();
        if (req.getFormType().equals(FormTypeEnum.ORDINARY)) {
            // 查询表单
            UserFormEntity userFormEntity = userFormService.getByKey(req.getFormKey());
            // 校验表单状态
            validateUserForm(userFormEntity);
        } else if (req.getFormType().equals(FormTypeEnum.ACTIVITY)) {
            // 活动表单
            ActivityEntity activity = activityMapper.selectOne(Wrappers.<ActivityEntity>lambdaQuery()
                .eq(ActivityEntity::getFormKey, req.getFormKey()));
            validateActivityForm(activity);
        }
        BeanUtil.copyProperties(req, formAnswerVO);
        return formAnswerVO;
    }

    private void validateActivityForm(ActivityEntity activity) {
        Assert.notNull(activity, () -> new ServiceException(MessageUtils.message("activity.not.exists")));
        Assert.isTrue(activity.getStatus().equals(ActivityStatusEnum.RELEASE), () -> new ServiceException(MessageUtils.message("activity.status.abnormal.fill")));
        Long answerUserId = LoginHelper.getUserId();
        // 校验是否可收集活动
        Assert.isTrue(activity.getFormEnabled(), () -> new ServiceException(MessageUtils.message("activity.collect.disabled")));
        // 校验答题时间范围是否正确,校验当前时间是否在范围内
        Date now = DateUtil.date();
        boolean isBetween = activity.getFormStartTime() != null && activity.getFormEndTime() != null &&
            (now.after(activity.getFormStartTime()) && now.before(activity.getFormEndTime()));
        Assert.isTrue(isBetween, () -> new ServiceException(MessageUtils.message("activity.not.in.time.range")));
        // 是否报名可填写
        if (activity.getFormJoinApplyEnabled()) {
            // 查询是否有报名记录
            boolean isExist = checkApplyRecord(activity, answerUserId);
            Assert.isFalse(isExist, () -> new ServiceException(MessageUtils.message("activity.need.apply.first")));
        }
        // 是否签到可填写
        if (activity.getFormJoinSigninEnabled()) {
            // 查询是否有签到记录
            boolean isExist = checkSigninRecord(activity, answerUserId);
            Assert.isFalse(isExist, () -> new ServiceException(MessageUtils.message("activity.need.signin.first")));
        }
    }

    private boolean checkSigninRecord(ActivityEntity activity, Long answerUserId) {
        return activityApplyMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
            .eq(ActivityApplyEntity::getActivityId, activity.getId())
            .eq(ActivityApplyEntity::getUserId, answerUserId)
            .eq(ActivityApplyEntity::getAuditState, ActivityStatusEnum.RELEASE)) > 0;
    }

    private boolean checkApplyRecord(ActivityEntity activity, Long answerUserId) {
        return activitySigninMapper.selectCount(Wrappers.<ActivitySigninEntity>lambdaQuery()
            .eq(ActivitySigninEntity::getActivityId, activity.getId())
            .eq(ActivitySigninEntity::getUserId, answerUserId)) > 0;
    }

    private void validateUserForm(UserFormEntity param) {
        Assert.notNull(param, () -> new ServiceException(MessageUtils.message("form.not.exists")));
        Assert.isTrue(param.getStatus().equals(FormStatusEnum.RELEASE), () -> new ServiceException(MessageUtils.message("form.status.abnormal.fill")));
        Long answerUserId = LoginHelper.getUserId();
        // 校验是否可收集问卷
        Assert.isTrue(param.getCollectEnabled(), () -> new ServiceException(MessageUtils.message("form.collect.disabled")));
        if (param.getValidateEnabled()) {
            // 校验答题时间范围是否正确,校验当前时间是否在范围内
            Date now = DateUtil.date();
            boolean isBetween = param.getValidateStartDate() != null && param.getValidateEndDate() != null &&
                (now.after(param.getValidateStartDate()) && now.before(param.getValidateEndDate()));
            Assert.isTrue(isBetween, () -> new ServiceException(MessageUtils.message("form.not.in.time.range")));
        }
        // 校验是否需要登录
        if (param.getNeedLogin()) {
            Assert.notNull(answerUserId, () -> new ServiceException(MessageUtils.message("form.need.login")));
            // 校验是否超过用户可答题次数
            if (param.getAnswerCountEnabled() && param.getAnswerCount() != 0) {
                // 统计该用户的答题次数
                long count = this.count(Wrappers.<UserFormDataEntity>lambdaQuery()
                    .eq(UserFormDataEntity::getFormKey, param.getFormKey())
                    .eq(UserFormDataEntity::getAnswerUserId, answerUserId));
                Assert.isTrue(count < param.getAnswerCount(), () -> new ServiceException(MessageUtils.message("form.exceed.user.count")));
            }
        }
        // 校验答题类型（0-都可答题;1-指定人）
        validateFormCollectLimitType(param.getCollectLimitType(), param.getFormKey(), answerUserId);

        // 校验是否超过答题总数限制
        if (param.getAnswerTotalCountEnabled() && param.getAnswerTotalCount() != 0) {
            // 校验答题总数是否超过上限
            long count = this.count(Wrappers.<UserFormDataEntity>lambdaQuery()
                .eq(UserFormDataEntity::getFormKey, param.getFormKey()));
            Assert.isTrue(count < param.getAnswerTotalCount(), () -> new ServiceException(MessageUtils.message("form.exceed.total.count")));
        }
    }

    /**
     * 校验答题类型
     *
     * @param collectLimitType 问卷实体
     * @param answerUserId     答题用户 ID
     */
    private void validateFormCollectLimitType(FormCollectLimitTypeEnum collectLimitType,
                                              String formKey,
                                              Long answerUserId) {
        if (collectLimitType.equals(FormCollectLimitTypeEnum.ALL)) {
            return;
        }
        Assert.notNull(answerUserId, () -> new ServiceException(MessageUtils.message("form.limited.users.login.required")));
        UserFormAuthEntity auth = userFormAuthMapper.selectOne(Wrappers.<UserFormAuthEntity>lambdaQuery()
            .eq(UserFormAuthEntity::getFormKey, formKey));
        Assert.notNull(auth, () -> new ServiceException(MessageUtils.message("form.limited.users.only")));
        if (CollUtil.isNotEmpty(auth.getUserIdList()) && !auth.getUserIdList().contains(answerUserId)) {
            if (auth.getDeptIdList().isEmpty() || !auth.getDeptIdList().contains(LoginHelper.getDeptId())) {
                throw new ServiceException(MessageUtils.message("form.limited.users.only"));
            }
        }
    }

    /***
     * 问卷的数据详情
     * @param req    详情查询参数
     * @return 详情数据
     */
    @Override
    public FormDataTableVO answerUserFormDetail(UserFormDataSearchRequest req) {
        return formDataUtils.search(req);
    }

    @Override
    public FormDataStatisticsVO answerStatistics(String formKey) {
        UserFormEntity form = userFormMapper.selectOne(new LambdaQueryWrapper<UserFormEntity>().eq(UserFormEntity::getFormKey, formKey));

        // 检查表单是否存在
        if (Objects.isNull(form)) {
            return FormDataStatisticsVO.builder().build();
        }
        formStateHandle(form);
        FormDataStatisticsVO vo = FormDataStatisticsVO.builder()
            .formId(form.getId())
            .formKey(formKey)
            .formName(form.getName())
            .status(form.getStatus())
            .validateEnabled(form.getValidateEnabled())
            .validateStartDate(form.getValidateStartDate())
            .validateEndDate(form.getValidateEndDate())
            .collectLimitType(form.getCollectLimitType())
            .build();

        QueryFormItemRequest request = new QueryFormItemRequest();
        request.setKey(form.getFormKey());
        vo.setFormItemList(userformItemService.queryFormItems(request));
        // 人数统计
        if (!form.getCollectLimitType().equals(FormCollectLimitTypeEnum.ALL)) {
            vo.setCollectLimitCount(getSpecifiedUserCount(form.getFormKey()));
        }
        vo.setCollectCount(getUniqueAnswerCount(form.getFormKey()).intValue());
        return vo;
    }

    private void formStateHandle(UserFormEntity item) {
        if (ObjectUtil.isNotNull(item.getStatus())
            && item.getValidateEnabled()
            && ObjectUtil.isNotNull(item.getValidateEndDate())
            && item.getValidateEndDate().before(DateUtil.date())
            && item.getStatus().equals(FormStatusEnum.RELEASE)) {
            item.setStatus(FormStatusEnum.END);
        }
    }

    @Override
    public FormDataStatTextVO answerStatisticsText(String formItemId, String formKey, PageQuery pageQuery) {
        // 1. 根据formItemId查询表单项信息
        UserFormItemEntity formItem = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormItemId, formItemId)
            .eq(UserFormItemEntity::getFormKey, formKey));
        Assert.notNull(formItem, () -> new ServiceException(MessageUtils.message("form.item.not.exists")));

        // 2. 验证表单项类型是否为文本类型
        String itemType = formItem.getType();
        Assert.isTrue(isTextType(itemType),
            () -> new ServiceException(MessageUtils.message("form.item.not.text.type", itemType)));

        // 3. 查询该表单项的所有答题详情数据用于去重统计
        List<UserFormDataDetailEntity> allDetails = userFormDataDetailMapper.selectList(
            Wrappers.<UserFormDataDetailEntity>lambdaQuery()
                .eq(UserFormDataDetailEntity::getFormItemId, formItemId)
                .eq(UserFormDataDetailEntity::getFormKey, formKey)
                .isNotNull(UserFormDataDetailEntity::getAnswerValue)
                .ne(UserFormDataDetailEntity::getAnswerValue, "")
        );

        // 4. 按create_by去重，每人只取最新的一份答题记录
        Map<Long, UserFormDataDetailEntity> uniqueAnswerMap = new HashMap<>();
        for (UserFormDataDetailEntity detail : allDetails) {
            Long createBy = detail.getCreateBy();
            if (createBy != null) {
                // 如果该用户还没有记录，或者当前记录的id更大（更新），则更新记录
                if (!uniqueAnswerMap.containsKey(createBy) ||
                    detail.getId() > uniqueAnswerMap.get(createBy).getId()) {
                    uniqueAnswerMap.put(createBy, detail);
                }
            }
        }

        // 5. 统计该表单的总答题人数
        long totalAnswerCount = getUniqueAnswerCount(formKey);

        // 6. 计算填写率（基于去重后的数据）
        long fillCount = uniqueAnswerMap.size(); // 填写该题的人数（去重后）
        String fillRate = PercentageUtils.formatPercentage(fillCount, totalAnswerCount);

        // 7. 处理分页显示去重后的数据
        List<UserFormDataDetailEntity> uniqueDetailsList = new ArrayList<>(uniqueAnswerMap.values());
        // 按ID降序排序，显示最新的记录
        uniqueDetailsList.sort((a, b) -> Long.compare(b.getId(), a.getId()));

        // 手动分页处理去重后的数据
        Page<UserFormDataDetailEntity> page = pageQuery.build();
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), uniqueDetailsList.size());
        List<UserFormDataDetailEntity> pagedUniqueDetails = uniqueDetailsList.subList(start, end);

        // 8. 转换为FormDataStatTextItemVO列表
        List<FormDataStatTextVO.FormDataStatTextItemVO> itemList = pagedUniqueDetails.stream()
            .map(detail -> FormDataStatTextVO.FormDataStatTextItemVO.builder()
                .id(detail.getId())
                .text(detail.getAnswerValue())
                .build())
            .toList();

        // 9. 构建表格数据信息（基于去重后的数据）
        TableDataInfo<FormDataStatTextVO.FormDataStatTextItemVO> tableDataInfo =
            new TableDataInfo<>(itemList, uniqueDetailsList.size());

        // 9. 构建返回对象
        return FormDataStatTextVO.builder()
            .fillRate(fillRate)
            .fillCount(fillCount)
            .tableDataInfo(tableDataInfo)
            .build();
    }

    @Override
    public FormDataStatSelectVO answerStatisticsSelect(String formItemId, String formKey) {
        // 1. 根据formItemId查询表单项信息
        UserFormItemEntity formItem = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormItemId, formItemId)
            .eq(UserFormItemEntity::getFormKey, formKey));
        Assert.notNull(formItem, () -> new ServiceException(MessageUtils.message("form.item.not.exists")));

        // 2. 验证表单项类型是否为单选或多选
        String itemType = formItem.getType();
        Assert.isTrue(isSupportedSelectType(itemType),
            () -> new ServiceException(MessageUtils.message("form.item.not.select.type", itemType)));

        // 3. 查询该表单项的所有答题详情数据
        List<UserFormDataDetailEntity> detailList = userFormDataDetailMapper.selectList(
            Wrappers.<UserFormDataDetailEntity>lambdaQuery()
                .eq(UserFormDataDetailEntity::getFormItemId, formItemId)
                .eq(UserFormDataDetailEntity::getFormKey, formKey)
                .isNotNull(UserFormDataDetailEntity::getAnswerValue)
                .ne(UserFormDataDetailEntity::getAnswerValue, "")
                .ne(UserFormDataDetailEntity::getAnswerValue, "[]"));

        // 4. 统计该表单的总答题人数
        long totalAnswerCount = getUniqueAnswerCount(formKey);

        // 5. 按create_by去重，每人只取最新的一份答题记录
        Map<Long, UserFormDataDetailEntity> uniqueAnswerMap = new HashMap<>();
        for (UserFormDataDetailEntity detail : detailList) {
            Long createBy = detail.getCreateBy();
            if (createBy != null) {
                // 如果该用户还没有记录，或者当前记录的id更大（更新），则更新记录
                if (!uniqueAnswerMap.containsKey(createBy) ||
                    detail.getId() > uniqueAnswerMap.get(createBy).getId()) {
                    uniqueAnswerMap.put(createBy, detail);
                }
            }
        }

        // 6. 解析去重后的答题数据并统计每个选项被选择的人数
        Map<String, Set<Long>> optionUserMap = new HashMap<>(); // 记录每个选项被哪些用户选择

        for (UserFormDataDetailEntity detail : uniqueAnswerMap.values()) {
            String answerValue = detail.getAnswerValue();
            if (StrUtil.isBlank(answerValue)) {
                continue;
            }

            List<String> selectedOptions = parseAnswerValue(answerValue, itemType);
            Long createBy = detail.getCreateBy();

            // 对于每个人的答题，统计他选择了哪些选项（每个选项每人只计数一次）
            for (String option : selectedOptions) {
                optionUserMap.computeIfAbsent(option, k -> new HashSet<>()).add(createBy);
            }
        }

        // 7. 转换为选项计数Map
        Map<String, Long> optionCountMap = new HashMap<>();
        for (Map.Entry<String, Set<Long>> entry : optionUserMap.entrySet()) {
            optionCountMap.put(entry.getKey(), (long) entry.getValue().size());
        }

        // 8. 计算该题的填写率（填写该题的人数 / 总答题人数）
        long fillCount = uniqueAnswerMap.size(); // 填写该题的人数（去重后）
        String fillRate = PercentageUtils.formatPercentage(fillCount, totalAnswerCount);

        // 7. 构建选项统计列表
        List<FormDataStatSelectVO.FormDataStatSelectItemVO> itemList = optionCountMap.entrySet().stream()
            .map(entry -> {
                String option = entry.getKey();
                Long count = entry.getValue(); // 选择该选项的人数
                // 选项填写率 = 选择该选项的人数 / 填写该题的总人数
                String optionFillRate = PercentageUtils.formatPercentage(count, fillCount);

                return FormDataStatSelectVO.FormDataStatSelectItemVO.builder()
                    .answerValue(option)
                    .itemType(itemType)
                    .fillRate(optionFillRate)
                    .fillCount(count)
                    .build();
            })
            // .sorted((a, b) -> Long.compare(b.getFillCount(), a.getFillCount())) // 按选择人数降序排列
            .toList();

        // 8. 构建返回对象
        return FormDataStatSelectVO.builder()
            .fillRate(fillRate)
            .fillCount(fillCount)
            .tableDataInfo(itemList)
            .build();
    }

    /**
     * 解析答题值，处理单选和多选的不同格式
     *
     * @param answerValue 答题值
     * @param itemType    表单项类型
     * @return 选项列表
     */
    private List<String> parseAnswerValue(String answerValue, String itemType) {
        if (isEmptyAnswer(answerValue)) {
            return new ArrayList<>();
        }

        // 单选类型：直接返回单个选项
        if (isSingleSelectType(itemType)) {
            return Arrays.asList(answerValue.trim());
        }

        // 多选类型：解析数组格式 [选项1, 选项2]
        if (isMultiSelectType(itemType)) {
            // 移除首尾的方括号
            String cleanValue = answerValue.trim();
            if (cleanValue.startsWith("[") && cleanValue.endsWith("]")) {
                cleanValue = cleanValue.substring(1, cleanValue.length() - 1);
            }

            // 如果去掉方括号后为空，返回空列表
            if (StrUtil.isBlank(cleanValue)) {
                return new ArrayList<>();
            }

            // 按逗号分割并清理空格
            return Arrays.stream(cleanValue.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .toList();
        }

        // 默认情况：当作单选处理
        return List.of(answerValue.trim());
    }

    @Override
    public FormDataStatDateVO answerStatisticsDate(String formItemId, String formKey, String statType) {
        // 1. 根据formItemId查询表单项信息
        UserFormItemEntity formItem = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormItemId, formItemId)
            .eq(UserFormItemEntity::getFormKey, formKey));
        Assert.notNull(formItem, () -> new ServiceException(MessageUtils.message("form.item.not.exists")));

        // 2. 验证表单项类型是否为日期类型
        String itemType = formItem.getType();
        Assert.isTrue(isDateType(itemType),
            () -> new ServiceException(MessageUtils.message("form.item.not.date.type", itemType)));

        // 3. 验证统计类型
        Assert.isTrue("year".equals(statType) || "month".equals(statType) || "day".equals(statType),
            () -> new ServiceException("不支持的统计类型：" + statType + "，支持的统计类型：year、month、day"));

        // 4. 查询该表单项的所有答题详情数据
        List<UserFormDataDetailEntity> allDetails = userFormDataDetailMapper.selectList(
            Wrappers.<UserFormDataDetailEntity>lambdaQuery()
                .eq(UserFormDataDetailEntity::getFormItemId, formItemId)
                .eq(UserFormDataDetailEntity::getFormKey, formKey)
                .isNotNull(UserFormDataDetailEntity::getAnswerValue)
                .ne(UserFormDataDetailEntity::getAnswerValue, "")
                .ne(UserFormDataDetailEntity::getAnswerValue, "[]"));

        // 5. 按create_by去重，每人只取最新的一份答题记录
        Map<Long, UserFormDataDetailEntity> uniqueAnswerMap = new HashMap<>();
        for (UserFormDataDetailEntity detail : allDetails) {
            Long createBy = detail.getCreateBy();
            if (createBy != null) {
                // 如果该用户还没有记录，或者当前记录的id更大（更新），则更新记录
                if (!uniqueAnswerMap.containsKey(createBy) ||
                    detail.getId() > uniqueAnswerMap.get(createBy).getId()) {
                    uniqueAnswerMap.put(createBy, detail);
                }
            }
        }

        // 6. 统计该表单的总答题人数
        long totalAnswerCount = getUniqueAnswerCount(formKey);

        // 7. 计算填写率（基于去重后的数据）
        long fillCount = uniqueAnswerMap.size(); // 填写该题的人数（去重后）
        String fillRate = PercentageUtils.formatPercentage(fillCount, totalAnswerCount);

        // 8. 根据统计类型处理日期数据（使用去重后的数据）
        List<UserFormDataDetailEntity> detailList = new ArrayList<>(uniqueAnswerMap.values());
        List<FormDataStatDateVO.FormDataStatDateItemVO> chartData = processDateStatistics(detailList, statType, fillCount);

        // 8. 构建返回对象
        return FormDataStatDateVO.builder()
            .fillRate(fillRate)
            .fillCount(fillCount)
            .statType(statType)
            .chartData(chartData)
            .build();
    }

    /**
     * 处理日期统计数据
     *
     * @param detailList     答题详情列表
     * @param statType       统计类型
     * @param totalFillCount 总填写数量
     * @return 图表数据
     */
    private List<FormDataStatDateVO.FormDataStatDateItemVO> processDateStatistics(
        List<UserFormDataDetailEntity> detailList, String statType, long totalFillCount) {

        Map<String, Long> dateCountMap = new HashMap<>();

        // 统计每个日期的填写次数
        for (UserFormDataDetailEntity detail : detailList) {
            String answerValue = detail.getAnswerValue();
            if (StrUtil.isBlank(answerValue)) {
                continue;
            }

            try {
                // 解析时间戳（毫秒）
                long timestamp = Long.parseLong(answerValue);
                // 将时间戳转换为LocalDate（使用系统默认时区）
                LocalDate date = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
                String dateKey = formatDateByStatType(date, statType);
                dateCountMap.merge(dateKey, 1L, Long::sum);
            } catch (Exception e) {
                // 忽略无法解析的时间戳
                log.warn("无法解析时间戳值: {}", answerValue);
            }
        }

        // 转换为图表数据并排序
        return dateCountMap.entrySet().stream()
            .map(entry -> {
                String dateKey = entry.getKey();
                Long count = entry.getValue();
                String itemFillRate = PercentageUtils.formatPercentage(count, totalFillCount);

                return FormDataStatDateVO.FormDataStatDateItemVO.builder()
                    .dateLabel(dateKey)
                    .dateValue(dateKey)
                    .count(count.intValue())
                    .fillRate(itemFillRate)
                    .build();
            })
            .sorted(Comparator.comparing(FormDataStatDateVO.FormDataStatDateItemVO::getDateValue)) // 按日期排序
            .collect(Collectors.toList());
    }

    /**
     * 根据统计类型格式化日期
     *
     * @param date     日期
     * @param statType 统计类型
     * @return 格式化后的日期字符串
     */
    private String formatDateByStatType(LocalDate date, String statType) {
        return switch (statType) {
            case "year" -> String.valueOf(date.getYear());
            case "month" -> date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case "day" -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            default -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        };
    }

    @Override
    public FormDataStatFileVO answerStatisticsFile(String formItemId, String formKey, PageQuery pageQuery) {
        // 1. 根据formItemId查询表单项信息
        UserFormItemEntity formItem = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormItemId, formItemId)
            .eq(UserFormItemEntity::getFormKey, formKey));
        Assert.notNull(formItem, () -> new ServiceException(MessageUtils.message("form.item.not.exists")));

        // 2. 验证表单项类型是否为文件类型
        String itemType = formItem.getType();
        Assert.isTrue(isFileType(itemType),
            () -> new ServiceException(MessageUtils.message("form.item.not.file.type", itemType)));

        // 3. 查询该表单项的所有答题详情数据用于去重统计
        List<UserFormDataDetailEntity> allDetails = userFormDataDetailMapper.selectList(
            Wrappers.<UserFormDataDetailEntity>lambdaQuery()
                .eq(UserFormDataDetailEntity::getFormItemId, formItemId)
                .eq(UserFormDataDetailEntity::getFormKey, formKey)
                .isNotNull(UserFormDataDetailEntity::getAnswerValue)
                .ne(UserFormDataDetailEntity::getAnswerValue, "")
                .ne(UserFormDataDetailEntity::getAnswerValue, "[]")
                .orderByDesc(UserFormDataDetailEntity::getCreateTime)
        );

        // 4. 按create_by去重，每人只取最新的一份答题记录
        Map<Long, UserFormDataDetailEntity> uniqueAnswerMap = new HashMap<>();
        for (UserFormDataDetailEntity detail : allDetails) {
            Long createBy = detail.getCreateBy();
            if (createBy != null) {
                // 如果该用户还没有记录，或者当前记录的id更大（更新），则更新记录
                if (!uniqueAnswerMap.containsKey(createBy) ||
                    detail.getId() > uniqueAnswerMap.get(createBy).getId()) {
                    uniqueAnswerMap.put(createBy, detail);
                }
            }
        }

        // 5. 统计该表单的总答题人数
        long totalAnswerCount = getUniqueAnswerCount(formKey);

        // 6. 计算填写率（基于去重后的数据）
        long fillCount = uniqueAnswerMap.size(); // 填写该题的人数（去重后）
        String fillRate = PercentageUtils.formatPercentage(fillCount, totalAnswerCount);

        // 7. 处理分页显示去重后的数据
        List<UserFormDataDetailEntity> uniqueDetailsList = new ArrayList<>(uniqueAnswerMap.values());
        // 按创建时间降序排序，显示最新的记录
        uniqueDetailsList.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));

        // 手动分页处理去重后的数据
        Page<UserFormDataDetailEntity> page = pageQuery.build();
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), uniqueDetailsList.size());
        List<UserFormDataDetailEntity> pagedUniqueDetails = uniqueDetailsList.subList(start, end);

        // 8. 解析文件数据并转换为文件列表（基于去重后的数据）
        List<FormDataStatFileVO.FormDataStatFileItemVO> fileList = new ArrayList<>();
        int totalFileCount = 0;

        for (UserFormDataDetailEntity detail : pagedUniqueDetails) {
            List<FormDataStatFileVO.FormDataStatFileItemVO> files = parseFileData(detail);
            fileList.addAll(files);
            totalFileCount += files.size();
        }

        // 9. 构建表格数据信息（基于去重后的数据）
        TableDataInfo<FormDataStatFileVO.FormDataStatFileItemVO> tableDataInfo =
            new TableDataInfo<>(fileList, uniqueDetailsList.size());

        // 9. 构建返回对象
        return FormDataStatFileVO.builder()
            .fillRate(fillRate)
            .fillCount((int) fillCount)
            .totalFileCount(totalFileCount)
            .tableDataInfo(tableDataInfo)
            .build();
    }

    /**
     * 解析文件数据
     *
     * @param detail 答题详情
     * @return 文件列表
     */
    private List<FormDataStatFileVO.FormDataStatFileItemVO> parseFileData(UserFormDataDetailEntity detail) {
        List<FormDataStatFileVO.FormDataStatFileItemVO> fileList = new ArrayList<>();
        String answerValue = detail.getAnswerValue();

        if (StrUtil.isBlank(answerValue)) {
            return fileList;
        }

        try {
            // 解析特殊格式的文件数据
            // 格式: [{uid=1020, name=文件名, size=4828, response={"code":200,"data":{...}}}]
            fileList = parseSpecialFormatFileData(answerValue, detail);
        } catch (Exception e) {
            log.warn("解析文件数据失败: {}", answerValue, e);
        }

        return fileList;
    }

    /**
     * 解析特殊格式的文件数据
     * 格式: [{uid=1020, name=文件名, size=4828, response={"code":200,"data":{...}}}]
     */
    private List<FormDataStatFileVO.FormDataStatFileItemVO> parseSpecialFormatFileData(String answerValue, UserFormDataDetailEntity detail) {
        List<FormDataStatFileVO.FormDataStatFileItemVO> fileList = new ArrayList<>();

        // 移除首尾的方括号
        String content = answerValue.trim();
        if (content.startsWith("[") && content.endsWith("}]")) {
            content = content.substring(1, content.length() - 1);
        }

        // 查找response字段的位置
        int responseIndex = content.indexOf("response=");
        if (responseIndex == -1) {
            return fileList;
        }

        try {
            // 提取response部分的JSON字符串
            String responseJson = content.substring(responseIndex + 9); // "response=".length() = 9

            // 解析response JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseNode = objectMapper.readTree(responseJson);

            if (responseNode.has("data")) {
                JsonNode dataNode = responseNode.get("data");

                String url = dataNode.has("url") ? dataNode.get("url").asText() : "";
                String fileName = dataNode.has("fileName") ? dataNode.get("fileName").asText() : "";
                String ossId = dataNode.has("ossId") ? dataNode.get("ossId").asText() : "";

                // 从前面的key=value部分提取size
                Long size = extractSizeFromKeyValuePart(content.substring(0, responseIndex));

                FormDataStatFileVO.FormDataStatFileItemVO fileItem = FormDataStatFileVO.FormDataStatFileItemVO.builder()
                    .id(detail.getId())
                    .fileName(fileName)
                    .url(url)
                    .ossId(ossId)
                    .size(size)
                    .build();

                fileList.add(fileItem);
            }
        } catch (Exception e) {
            log.warn("解析特殊格式文件数据失败: {}", answerValue, e);
        }

        return fileList;
    }

    /**
     * 从key=value格式的字符串中提取size值
     */
    private Long extractSizeFromKeyValuePart(String keyValuePart) {
        try {
            // 查找size=xxx的模式
            String[] pairs = keyValuePart.split(",\\s*");
            for (String pair : pairs) {
                String trimmedPair = pair.trim();
                if (trimmedPair.startsWith("size=")) {
                    String sizeStr = trimmedPair.substring(5); // "size=".length() = 5
                    return Long.parseLong(sizeStr);
                }
            }
        } catch (Exception e) {
            log.warn("提取size失败: {}", keyValuePart, e);
        }
        return 0L;
    }

    @Override
    public void downloadAllFiles(String formItemId, String formKey, HttpServletResponse response) throws Exception {
        // 1. 根据formItemId查询表单项信息
        UserFormItemEntity formItem = userFormItemMapper.selectOne(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormItemId, formItemId)
            .eq(UserFormItemEntity::getFormKey, formKey));
        Assert.notNull(formItem, () -> new ServiceException(MessageUtils.message("form.item.not.exists")));

        // 2. 验证表单项类型是否为文件类型
        String itemType = formItem.getType();
        Assert.isTrue(isFileType(itemType),
            () -> new ServiceException(MessageUtils.message("form.item.not.file.type", itemType)));

        // 3. 查询该表单项的所有文件数据（使用formKey过滤）
        List<UserFormDataDetailEntity> allDetails = userFormDataDetailMapper.selectList(
            Wrappers.<UserFormDataDetailEntity>lambdaQuery()
                .eq(UserFormDataDetailEntity::getFormItemId, formItemId)
                .eq(UserFormDataDetailEntity::getFormKey, formKey)
                .isNotNull(UserFormDataDetailEntity::getAnswerValue)
                .ne(UserFormDataDetailEntity::getAnswerValue, "")
                .ne(UserFormDataDetailEntity::getAnswerValue, "[]")
                .orderByDesc(UserFormDataDetailEntity::getCreateTime)
        );

        // 4. 按create_by去重，每人只取最新的一份答题记录
        Map<Long, UserFormDataDetailEntity> uniqueAnswerMap = new HashMap<>();
        for (UserFormDataDetailEntity detail : allDetails) {
            Long createBy = detail.getCreateBy();
            if (createBy != null) {
                // 如果该用户还没有记录，或者当前记录的id更大（更新），则更新记录
                if (!uniqueAnswerMap.containsKey(createBy) ||
                    detail.getId() > uniqueAnswerMap.get(createBy).getId()) {
                    uniqueAnswerMap.put(createBy, detail);
                }
            }
        }

        List<UserFormDataDetailEntity> detailList = new ArrayList<>(uniqueAnswerMap.values());

        if (detailList.isEmpty()) {
            throw new ServiceException(MessageUtils.message("form.item.no.file.data"));
        }

        // 4. 解析所有文件数据，收集ossId
        List<FileDownloadInfo> fileInfoList = new ArrayList<>();
        for (UserFormDataDetailEntity detail : detailList) {
            List<FormDataStatFileVO.FormDataStatFileItemVO> files = parseFileData(detail);
            for (FormDataStatFileVO.FormDataStatFileItemVO file : files) {
                if (StrUtil.isNotBlank(file.getOssId())) {
                    FileDownloadInfo fileInfo = new FileDownloadInfo();
                    fileInfo.ossId = file.getOssId();
                    fileInfo.fileName = file.getFileName();
                    fileInfo.originalFileName = file.getFileName();
                    fileInfoList.add(fileInfo);
                }
            }
        }

        if (fileInfoList.isEmpty()) {
            throw new ServiceException(MessageUtils.message("form.no.valid.file.data"));
        }

        // 5. 创建压缩包并下载
        createAndDownloadZip(fileInfoList, formItem.getLabel(), response);
    }

    /**
     * 创建压缩包并下载
     */
    private void createAndDownloadZip(List<FileDownloadInfo> fileInfoList, String label, HttpServletResponse response) throws Exception {
        // 设置响应头
        String zipFileName = (StrUtil.isNotBlank(label) ? label : "附件") + "_" + System.currentTimeMillis() + ".zip";
        response.setContentType("application/zip");

        // 对文件名进行UTF-8编码处理，支持中文和特殊字符
        String encodedFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"; filename*=UTF-8''" + encodedFileName);

        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            Map<String, Integer> fileNameCountMap = new HashMap<>();

            for (FileDownloadInfo fileInfo : fileInfoList) {
                try {
                    // 根据ossId获取文件信息
                    SysOssVo ossVo = sysOssService.getById(Long.parseLong(fileInfo.ossId));
                    if (ossVo == null) {
                        log.warn("OSS文件不存在: {}", fileInfo.ossId);
                        continue;
                    }

                    // 获取OSS客户端并下载文件
                    OssClient ossClient = OssFactory.instance(ossVo.getService());

                    // 处理重名文件
                    String entryName = getUniqueFileName(fileInfo.fileName, fileNameCountMap);

                    // 创建ZIP条目
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipOut.putNextEntry(zipEntry);

                    try {
                        // 使用download方法直接写入到ZipOutputStream
                        ossClient.download(ossVo.getFileName(), zipOut, null);
                        zipOut.closeEntry();
                        log.info("成功添加文件到压缩包: {}", entryName);

                    } catch (Exception e) {
                        log.error("下载文件失败: ossId={}, fileName={}", fileInfo.ossId, fileInfo.fileName, e);
                        // 如果当前条目已经打开，需要关闭它
                        try {
                            zipOut.closeEntry();
                        } catch (Exception ignored) {
                        }
                    }

                } catch (Exception e) {
                    log.error("处理文件失败: ossId={}, fileName={}", fileInfo.ossId, fileInfo.fileName, e);
                }
            }

            zipOut.finish();
            log.info("压缩包创建完成: {}", zipFileName);
        }
    }

    /**
     * 获取唯一的文件名（处理重名文件）
     */
    private String getUniqueFileName(String originalFileName, Map<String, Integer> fileNameCountMap) {
        if (StrUtil.isBlank(originalFileName)) {
            originalFileName = "未知文件";
        }

        String fileName = originalFileName;
        Integer count = fileNameCountMap.get(fileName);

        if (count == null) {
            fileNameCountMap.put(fileName, 1);
            return fileName;
        } else {
            // 文件名重复，添加序号
            count++;
            fileNameCountMap.put(originalFileName, count);

            // 分离文件名和扩展名
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                String nameWithoutExt = fileName.substring(0, lastDotIndex);
                String extension = fileName.substring(lastDotIndex);
                return nameWithoutExt + "(" + count + ")" + extension;
            } else {
                return fileName + "(" + count + ")";
            }
        }
    }

    /**
     * 计算指定填写人数
     * 根据user_form_auth表中的user_id_list和dept_id_list计算出指定填写人数
     * 注意：避免重复计算（用户既在指定用户列表中，又在指定部门中的情况）
     *
     * @param formKey 表单key
     * @return 指定填写人数
     */
    private Integer getSpecifiedUserCount(String formKey) {
        // 查询表单授权信息
        UserFormAuthEntity auth = userFormAuthMapper.selectOne(Wrappers.<UserFormAuthEntity>lambdaQuery()
            .eq(UserFormAuthEntity::getFormKey, formKey));

        if (auth == null) {
            return null;
        }

        // 使用Set来避免重复计算用户
        Set<Long> userIdSet = new HashSet<>();

        // 添加指定用户ID
        if (CollUtil.isNotEmpty(auth.getUserIdList())) {
            userIdSet.addAll(auth.getUserIdList());
        }

        // 添加指定部门下的用户ID
        if (CollUtil.isNotEmpty(auth.getDeptIdList())) {
            for (Long deptId : auth.getDeptIdList()) {
                List<SysUserVo> deptUsers = sysUserService.selectUserListByDept(deptId);
                for (SysUserVo user : deptUsers) {
                    userIdSet.add(user.getUserId());
                }
            }
        }

        return userIdSet.size();
    }

    /**
     * 计算唯一回答人数
     * 根据user_form_data表的create_by字段统计回答人数
     * 同一个form_key下相同create_by只算作1份填写
     * 性能优化：只查询create_by字段并去重，避免查询完整记录
     *
     * @param formKey 表单key
     * @return 唯一回答人数
     */
    private Long getUniqueAnswerCount(String formKey) {
        // 只查询create_by字段，减少数据传输量
        List<UserFormDataEntity> createByList = baseMapper.selectList(
            Wrappers.<UserFormDataEntity>lambdaQuery()
                .select(UserFormDataEntity::getCreateBy)
                .eq(UserFormDataEntity::getFormKey, formKey)
                .isNotNull(UserFormDataEntity::getCreateBy)
        );

        return createByList.stream()
            .map(UserFormDataEntity::getCreateBy)
            .distinct()
            .count();
    }

    /**
     * 文件下载信息
     */
    private static class FileDownloadInfo {
        String ossId;
        String fileName;
        String originalFileName;
    }
}
