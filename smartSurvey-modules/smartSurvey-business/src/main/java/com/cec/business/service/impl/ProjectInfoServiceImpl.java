package com.cec.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cec.business.domain.bo.ProjectInfo;
import com.cec.business.domain.bo.ProjectMemberRole;
import com.cec.business.domain.bo.ProjectRole;
import com.cec.business.domain.req.ProjectInfoReq;
import com.cec.business.domain.vo.ProjectInfoVo;
import com.cec.business.mapper.ProjectInfoMapper;
import com.cec.business.mapper.ProjectMemberRoleMapper;
import com.cec.business.mapper.ProjectRoleMapper;
import com.cec.business.service.IProjectInfoService;
import com.cec.common.core.constant.TenantConstants;
import com.cec.common.core.domain.dto.RoleDTO;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.vo.SysDeptVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.service.ISysDeptService;
import com.cec.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProjectInfoServiceImpl implements IProjectInfoService {

    /**
     * 项目管理员角色键
     */
    private static final String ROLE_KEY_PROJECT_MANAGER = "project_manager";
    /**
     * 项目成员角色键
     */
    public static final String ROLE_KEY_PROJECT_MEMBER = "project_member";
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectMemberRoleMapper projectMemberRoleMapper;
    private final ProjectRoleMapper projectRoleMapper;
    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;

    /**
     * 查询项目信息
     *
     * @param projectId 主键
     * @return 项目信息
     */
    @Override
    public ProjectInfoVo queryById(Long projectId) {
        ProjectInfoVo projectInfoVo = projectInfoMapper.selectVoById(projectId);
        if (projectInfoVo == null) {
            throw new ServiceException(MessageUtils.message("project.not.exists"));
        }

        // 设置当前用户在项目中的角色
        try {
            String roleKey = checkProjectRoleKey(projectInfoVo.getProjectId());
            projectInfoVo.setRoleKey(roleKey);
        } catch (ServiceException e) {
            // 用户没有项目权限，但可能是超管或者其他有查看权限的角色
            projectInfoVo.setRoleKey(null);
        }

        // 查询项目管理员
        List<SysUserVo> managerUsers = getUsersByProjectAndRole(projectId, ROLE_KEY_PROJECT_MANAGER);
        projectInfoVo.setProjectManagerUsers(managerUsers);

        // 查询项目成员
        List<SysUserVo> memberUsers = getUsersByProjectAndRole(projectId, ROLE_KEY_PROJECT_MEMBER);
        projectInfoVo.setProjectMemberUsers(memberUsers);

        // 查询项目关联部门
        List<SysDeptVo> memberDepts = getDeptsByProject(projectId);
        projectInfoVo.setProjectMemberDepts(memberDepts);

        return projectInfoVo;
    }

    /**
     * 根据项目ID和角色获取用户列表
     *
     * @param projectId 项目ID
     * @param roleKey   角色键
     * @return 用户列表
     */
    private List<SysUserVo> getUsersByProjectAndRole(Long projectId, String roleKey) {
        // 查询项目-角色-用户关联关系
        LambdaQueryWrapper<ProjectMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectMemberRole::getProjectId, projectId)
            .eq(ProjectMemberRole::getRoleKey, roleKey)
            .isNotNull(ProjectMemberRole::getUserId);

        List<ProjectMemberRole> memberRoles = projectMemberRoleMapper.selectList(queryWrapper);

        // 提取用户ID并查询用户信息
        List<Long> userIds = memberRoles.stream()
            .map(ProjectMemberRole::getUserId)
            .distinct()
            .toList();

        if (userIds.isEmpty()) {
            return List.of();
        }

        // 调用系统用户服务获取用户信息
        return sysUserService.selectUserByIds(userIds, null);
    }

    /**
     * 获取项目关联的部门列表
     *
     * @param projectId 项目ID
     * @return 部门列表
     */
    private List<SysDeptVo> getDeptsByProject(Long projectId) {
        // 查询项目-角色-部门关联关系
        LambdaQueryWrapper<ProjectMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectMemberRole::getProjectId, projectId)
            .isNotNull(ProjectMemberRole::getDeptId);

        List<ProjectMemberRole> memberRoles = projectMemberRoleMapper.selectList(queryWrapper);

        // 提取部门ID并查询部门信息
        List<Long> deptIds = memberRoles.stream()
            .map(ProjectMemberRole::getDeptId)
            .distinct()
            .toList();

        if (deptIds.isEmpty()) {
            return List.of();
        }

        // 调用系统部门服务获取部门信息
        return sysDeptService.selectDeptByIds(deptIds);
    }

    /**
     * 分页查询项目信息列表
     *
     * @param bo 查询条件
     * @return 项目信息列表
     */
    @Override
    public List<ProjectInfoVo> queryPageList(ProjectInfoReq bo) {
        LambdaQueryWrapper<ProjectInfo> lqw = buildQueryWrapper(bo);
        // 添加项目权限过滤
        addPermissionFilter(lqw);
        List<ProjectInfoVo> projectInfoVos = projectInfoMapper.selectVoList(lqw);
        projectInfoVos.forEach(projectInfoVo -> projectInfoVo.setRoleKey(checkProjectRoleKey(projectInfoVo.getProjectId())));
        return projectInfoVos;
    }

    /**
     * 查询符合条件的项目信息列表
     *
     * @param bo 查询条件
     * @return 项目信息列表
     */
    @Override
    public List<ProjectInfoVo> queryList(ProjectInfoReq bo) {
        LambdaQueryWrapper<ProjectInfo> lqw = buildQueryWrapper(bo);
        // 添加项目权限过滤
        addPermissionFilter(lqw);
        List<ProjectInfoVo> projectInfoVos = projectInfoMapper.selectVoList(lqw);
        projectInfoVos.forEach(projectInfoVo -> {
            projectInfoVo.setRoleKey(checkProjectRoleKey(projectInfoVo.getProjectId()));
        });

        return projectInfoVos;
    }

    private LambdaQueryWrapper<ProjectInfo> buildQueryWrapper(ProjectInfoReq bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProjectInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ProjectInfo::getProjectId);
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), ProjectInfo::getProjectName, bo.getProjectName());
//        lqw.eq(StringUtils.isNotBlank(bo.getProjectCode()), ProjectInfo::getProjectCode, bo.getProjectCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ProjectInfo::getContent, bo.getContent());
//        lqw.eq(bo.getCreateUserId() != null, ProjectInfo::getCreateUserId, bo.getCreateUserId());
//        lqw.eq(StringUtils.isNotBlank(bo.getCreateStaffNum()), ProjectInfo::getCreateStaffNum, bo.getCreateStaffNum());
//        lqw.like(StringUtils.isNotBlank(bo.getCreateStaffName()), ProjectInfo::getCreateStaffName, bo.getCreateStaffName());
        return lqw;
    }

    /**
     * 添加权限过滤条件
     *
     * @param lqw 查询条件构造器
     */
    private void addPermissionFilter(LambdaQueryWrapper<ProjectInfo> lqw) {
        // 获取当前登录用户
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(MessageUtils.message("project.user.not.login"));
        }

        Long userId = loginUser.getUserId();

        // 超级管理员可查看所有项目
        if (LoginHelper.isSuperAdmin()) {
            return;
        }

        // 检查用户角色是否包含超级管理员或租户管理员角色
        if (loginUser.getRoles() != null) {
            boolean isSuperAdmin = loginUser.getRoles().stream()
                .anyMatch(role -> TenantConstants.SUPER_ADMIN_ROLE_KEY.equals(role.getRoleKey()));

            if (isSuperAdmin) {
                return;
            }
        }

        // 查询用户关联的项目ID列表（作为项目管理员或项目成员）
        // 使用子查询方式，减少多次数据库查询
        lqw.and(wrapper ->
            wrapper.inSql(ProjectInfo::getProjectId,
                    "select distinct project_id from project_member_role where user_id = " + userId)
                .or()
                .inSql(ProjectInfo::getProjectId,
                    "select distinct project_id from project_member_role where dept_id = " + loginUser.getDeptId())
        ).or().eq(ProjectInfo::getCreateBy, userId);
    }

    /**
     * 新增项目信息
     *
     * @param req 项目信息
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ProjectInfoReq req) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        ProjectInfo projectInfo = new ProjectInfo();
        projectInfo.setProjectId(IdWorker.getId())
            .setProjectName(req.getProjectName())
            .setCreateUserId(loginUser.getUserId())
            .setCreateStaffNum(loginUser.getStaffNum())
            .setCreateStaffName(loginUser.getStaffName());
        validEntityBeforeSave(projectInfo);
        projectInfoMapper.insert(projectInfo);

        List<ProjectRole> projectRoles = getProjectRoles();
        Map<String, ProjectRole> roleMap = projectRoles.stream().collect(Collectors.toMap(ProjectRole::getRoleKey, role -> role, (a, b) -> a));

        // 获取角色
        ProjectRole managerRole = roleMap.get(ROLE_KEY_PROJECT_MANAGER);
        ProjectRole memberRole = roleMap.get(ROLE_KEY_PROJECT_MEMBER);

        // 为项目分配各类角色
        // 把当前登录者放到项目的管理员中
        Long[] userIds = ArrayUtils.add(req.getProjectManagerUserIds(), 0, loginUser.getUserId());
        log.info("项目[{}]管理员用户ID:{}", projectInfo.getProjectId(), userIds);
        assignProjectRoles(projectInfo.getProjectId(), managerRole, userIds, null, "管理员");
        assignProjectRoles(projectInfo.getProjectId(), memberRole, req.getProjectMemberUserIds(), null, "成员");
        assignProjectRoles(projectInfo.getProjectId(), memberRole, null, req.getProjectDeptIds(), "部门成员");

        return true;
    }

    /**
     * 分配项目角色
     *
     * @param projectId    项目ID
     * @param role         角色信息
     * @param userIds      用户ID数组
     * @param deptIds      部门ID数组
     * @param roleTypeName 角色类型名称(用于日志)
     */
    private void assignProjectRoles(Long projectId, ProjectRole role, Long[] userIds, Long[] deptIds, String roleTypeName) {
        // 检查角色是否存在
        if (role == null) {
            log.warn("项目[{}]分配{}角色失败: 角色不存在", projectId, roleTypeName);
            return;
        }

        // 分配用户角色
        if (userIds != null && userIds.length > 0) {
            log.info("为项目[{}]分配{}个{}", projectId, userIds.length, roleTypeName);
            List<ProjectMemberRole> memberRoles = Arrays.stream(userIds)
                .map(userId -> createMemberRole(projectId, role, userId, null))
                .toList();

            if (!memberRoles.isEmpty()) {
                projectMemberRoleMapper.insertBatch(memberRoles);
            }
        }

        // 分配部门角色
        if (deptIds != null && deptIds.length > 0) {
            log.info("为项目[{}]分配{}个{}部门", projectId, deptIds.length, roleTypeName);
            List<ProjectMemberRole> memberRoles = Arrays.stream(deptIds)
                .map(deptId -> createMemberRole(projectId, role, null, deptId))
                .toList();

            if (!memberRoles.isEmpty()) {
                projectMemberRoleMapper.insertBatch(memberRoles);
            }
        }
    }

    /**
     * 创建项目成员角色对象
     *
     * @param projectId 项目ID
     * @param role      角色信息
     * @param userId    用户ID
     * @param deptId    部门ID
     * @return 项目成员角色对象
     */
    private ProjectMemberRole createMemberRole(Long projectId, ProjectRole role, Long userId, Long deptId) {
        return new ProjectMemberRole()
            .setProjectId(projectId)
            .setRoleId(role.getRoleId())
            .setRoleName(role.getRoleName())
            .setRoleKey(role.getRoleKey())
            .setUserId(userId)
            .setDeptId(deptId);
    }

    /**
     * 获取项目角色列表
     *
     * @return 项目角色列表
     */
    private List<ProjectRole> getProjectRoles() {
        return projectRoleMapper.selectList();
    }

    /**
     * 修改项目信息
     *
     * @param req 项目信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProjectInfoReq req) {

        String roleKey = checkProjectRoleKey(req.getProjectId());
        if (roleKey.equals(ROLE_KEY_PROJECT_MEMBER)) {
            throw new ServiceException(MessageUtils.message("project.no.permission.modify"));
        }

        ProjectInfo projectInfo = projectInfoMapper.selectById(req.getProjectId());
        if (ObjectUtils.isEmpty(projectInfo)) {
            throw new ServiceException(MessageUtils.message("project.not.exists"));
        }

        projectInfo.setProjectName(req.getProjectName());
        validEntityBeforeSave(projectInfo);
        projectInfoMapper.updateById(projectInfo);

        List<ProjectRole> projectRoles = getProjectRoles();
        Map<String, ProjectRole> roleMap = projectRoles.stream().collect(Collectors.toMap(ProjectRole::getRoleKey, role -> role, (a, b) -> a));

        // 获取角色
        ProjectRole managerRole = roleMap.get(ROLE_KEY_PROJECT_MANAGER);
        ProjectRole memberRole = roleMap.get(ROLE_KEY_PROJECT_MEMBER);

        projectMemberRoleMapper.delete(new LambdaQueryWrapper<ProjectMemberRole>()
            .eq(ProjectMemberRole::getProjectId, projectInfo.getProjectId())
        );

        // 为项目分配各类角色
        Long[] userIds = ArrayUtils.add(req.getProjectManagerUserIds(), 0, LoginHelper.getUserId());
        log.info("项目[{}]管理员用户ID:{}", projectInfo.getProjectId(), userIds);
        assignProjectRoles(projectInfo.getProjectId(), managerRole, userIds, null, "管理员");
        assignProjectRoles(projectInfo.getProjectId(), memberRole, req.getProjectMemberUserIds(), null, "成员");
        assignProjectRoles(projectInfo.getProjectId(), memberRole, null, req.getProjectDeptIds(), "部门成员");

        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProjectInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验项目角色权限
     *
     * @param projectId 项目ID
     * @return 用户在项目中的角色键
     * @throws ServiceException 如果当前用户没有权限访问该项目
     */
    @Override
    public String checkProjectRoleKey(Long projectId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(MessageUtils.message("project.user.not.login.access"));
        }

        Long userId = loginUser.getUserId();

        // 1. 判断是否为超级管理员
        if (LoginHelper.isSuperAdmin()) {
            // 超级管理员，直接放行
            return TenantConstants.SUPER_ADMIN_ROLE_KEY;
        }

        // 校验是否为该项目的创建者
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            throw new ServiceException(MessageUtils.message("project.not.exists"));
        }
        if (projectInfo.getCreateBy().equals(userId)) {
            return ROLE_KEY_PROJECT_MANAGER;
        }

        // 检查用户角色是否包含超级管理员或租户管理员角色
        if (loginUser.getRoles() != null) {
            String superRole = loginUser.getRoles().stream()
                .map(RoleDTO::getRoleKey)
                .filter(TenantConstants.SUPER_ADMIN_ROLE_KEY::equals)
                .findFirst()
                .orElse(null);

            if (superRole != null) {
                return superRole;
            }
        }

        // 2. 判断是否为项目管理员
        LambdaQueryWrapper<ProjectMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectMemberRole::getProjectId, projectId)
            .eq(ProjectMemberRole::getUserId, userId)
            .eq(ProjectMemberRole::getRoleKey, ROLE_KEY_PROJECT_MANAGER);

        if (projectMemberRoleMapper.exists(queryWrapper)) {
            // 项目管理员，有权限
            return ROLE_KEY_PROJECT_MANAGER;
        }

        // 3. 判断是否为项目成员
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectMemberRole::getProjectId, projectId)
            .eq(ProjectMemberRole::getUserId, userId)
            .eq(ProjectMemberRole::getRoleKey, ROLE_KEY_PROJECT_MEMBER);

        if (projectMemberRoleMapper.exists(queryWrapper)) {
            // 项目成员，有权限
            return ROLE_KEY_PROJECT_MEMBER;
        }

        // 4. 判断用户是否属于项目关联的部门
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectMemberRole::getProjectId, projectId)
            .isNotNull(ProjectMemberRole::getDeptId)
            .eq(ProjectMemberRole::getRoleKey, ROLE_KEY_PROJECT_MEMBER)
            .eq(ProjectMemberRole::getDeptId, loginUser.getDeptId());

        if (projectMemberRoleMapper.exists(queryWrapper)) {
            // 用户所在部门是项目成员部门，有权限
            return ROLE_KEY_PROJECT_MEMBER;
        }

        // 没有任何角色权限，抛出异常
        throw new ServiceException(MessageUtils.message("project.no.permission.access"));
    }

    /**
     * 校验并批量删除项目信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 检查权限并删除项目
        for (Long id : ids) {
            // 检查用户权限
            String roleKey = checkProjectRoleKey(id);
            if (ROLE_KEY_PROJECT_MEMBER.equals(roleKey)) {
                throw new ServiceException(MessageUtils.message("project.no.permission.delete", id));
            }

            // 删除项目成员角色关联数据
            LambdaQueryWrapper<ProjectMemberRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectMemberRole::getProjectId, id);
            projectMemberRoleMapper.delete(queryWrapper);

            // 删除项目信息
            projectInfoMapper.deleteById(id);
            log.info("删除项目成功，项目ID:{}", id);
        }

        return true;
    }
}
