package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description : 表单状态
 * @create : 2020-12-04 13:35
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FormStatusEnum implements IDictEnum<Integer> {
    CREATE(1, "待发布"),
    RELEASE(2, "进行中"),
    END(3, "已结束"),
    STOP(4, "已暂停");


    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static FormStatusEnum findByValue(int value) {
        for (FormStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    public static FormStatusEnum fromString(String value) {
        try {
            // 如果传入的是字符串，可以先转换成数字
            return findByValue(Integer.parseInt(value));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid value for DataSourceEnum: " + value);
        }
    }
}
