package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 活动成员角色对象 activity_member_role
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableName("activity_member_role")
@Accessors(chain = true)
public class ActivityMemberRole {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 活动ID
     */
    // @TableId(value = "project_id")
    private Long activityId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;


}
