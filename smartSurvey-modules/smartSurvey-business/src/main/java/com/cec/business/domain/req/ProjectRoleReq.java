package com.cec.business.domain.req;

import com.cec.business.domain.bo.ProjectRole;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目角色信息业务对象 project_role
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectRole.class, reverseConvertGenerate = false)
public class ProjectRoleReq extends BaseEntity {

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;

    /**
     * 显示顺序
     */
    private Long roleSort;

    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    @NotBlank(message = "{validation.data.scope.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String dataScope;

    /**
     * 菜单树选择项是否关联显示
     */
    @NotNull(message = "{validation.menu.check.strictly.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Long menuCheckStrictly;

    /**
     * 部门树选择项是否关联显示
     */
    @NotNull(message = "{validation.dept.check.strictly.not.null}", groups = {AddGroup.class, EditGroup.class})
    private Long deptCheckStrictly;

    /**
     * 角色状态（0正常 1停用）
     */
    private String status;

    /**
     * 备注
     */
    @NotBlank(message = "{validation.remark.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
