package com.cec.business.domain.vo;

import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 问卷的文本数据统计，适用于text-input/textarea/number-input
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Accessors(chain = true)
public class FormDataStatTextVO {

    /**
     * 填写率（带百分号，如：85.5%）
     */
    private String fillRate;

    /**
     * 填写数量
     */
    private Long fillCount;

    /**
     * 表格数据
     */
    TableDataInfo<FormDataStatTextItemVO> tableDataInfo;

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class FormDataStatTextItemVO {

        /**
         * 答案id。没什么用
         */
        private Long id;
        /**
         * 答案内容
         */
        private String text;
    }

}
