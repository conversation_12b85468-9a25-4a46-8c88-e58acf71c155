package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.business.domain.enums.*;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.UpdateGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 用户表单表(Form)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName(value = "user_form", autoResultMap = true)
public class UserFormEntity extends BaseEntity {

    @NotNull(message = "{common.id.not.blank}", groups = {EditGroup.class})
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "{form.projectId.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private Long projectId;

    /**
     * 表单code
     */
    @NotBlank(message = "{form.formKey.not.blank}", groups = {UpdateGroup.class})
    private String formKey;

    /**
     * 表单名称
     */
    @NotBlank(message = "{form.name.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    @Size(max = 1000, message = "{form.name.size}")
    private String name;

    /**
     * 表单描述
     */
    private String description;

    /**
     * 表单来源
     */
    private FormSourceTypeEnum sourceType;

    /**
     * 来源ID
     */
    private String sourceId;

    /***
     * 状态
     */
    private FormStatusEnum status;

    /**
     * 表单类型
     */
    private FormTypeEnum type;

    @TableField(value = "is_deleted", typeHandler = BooleanTypeHandler.class)
    private Boolean deleted;

    /**
     * 是否可收集(1-是；0-否)
     */
    @TableField(value = "collect_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean collectEnabled = Boolean.TRUE;

    /**
     * 答题类型（0-都可答题;1-指定人）
     */
    private FormCollectLimitTypeEnum collectLimitType;

    /**
     * 是否需要登录(1-是；0-否)
     */
    @TableField(value = "need_login", typeHandler = BooleanTypeHandler.class)
    private Boolean needLogin;

    /**
     * 匿名是否可填写(1-是；0-否)
     */
    @TableField(value = "anonymous_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean anonymousEnabled;

    /**
     * 是否限制答题次数(1-是；0-否)
     */
    @TableField(value = "answer_count_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean answerCountEnabled;

    /**
     * 答题次数(0-无限制)
     */
    private Integer answerCount = 0;

    /**
     * 设定问卷总数量上限(1-是；0-否)
     */
    @TableField(value = "answer_total_count_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean answerTotalCountEnabled;

    /**
     * 问卷总数量上限(0-无限制)
     */
    private Integer answerTotalCount = 0;

    /**
     * 答题是否有有效期(1-是；0-否)
     */
    @TableField(value = "validate_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean validateEnabled;

    /**
     * 答题开始时间
     */
    private Date validateStartDate;

    /**
     * 答题结束时间
     */
    private Date validateEndDate;

    /**
     * 是否可预览答卷(1-是；0-否)
     */
    @TableField(value = "preview_enabled", typeHandler = BooleanTypeHandler.class)
    private Boolean previewEnabled;

    /**
     * 跳转类型(1-默认页；2-自定义富文本页；3-指定页面)
     */
    private FormJumpTypeEnum jumpType;

    /**
     * 跳转内容
     */
    private String jumpContent;

    /**
     * 指定页面路径
     */
    private String jumpUrl;

    /***
     * 指定人ID列表
     */
    @TableField(exist = false)
    private List<Long> userIdList;

    /***
     * 指定部门ID列表
     */
    @TableField(exist = false)
    private List<Long> deptIdList;

    /***
     * 已回答的问卷数量
     */
    @TableField(exist = false)
    private Long answeredCount;

    /***
     * 发布后的URL
     */
    private String publishUrl;

    /***
     * 二维码图片
     */
    private String qrCodeUrl;

}
