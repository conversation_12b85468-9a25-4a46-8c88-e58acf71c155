package com.cec.business.service;

import com.cec.business.domain.req.ProjectInfoReq;
import com.cec.business.domain.vo.ProjectInfoVo;
import com.cec.common.core.exception.ServiceException;

import java.util.Collection;
import java.util.List;

/**
 * 项目信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
public interface IProjectInfoService {

    /**
     * 查询项目信息
     *
     * @param projectId 主键
     * @return 项目信息
     */
    ProjectInfoVo queryById(Long projectId);

    /**
     * 分页查询项目信息列表
     *
     * @param bo 查询条件
     * @return 项目信息分页列表
     */
    List<ProjectInfoVo> queryPageList(ProjectInfoReq bo);

    /**
     * 查询符合条件的项目信息列表
     *
     * @param bo 查询条件
     * @return 项目信息列表
     */
    List<ProjectInfoVo> queryList(ProjectInfoReq bo);

    /**
     * 新增项目信息
     *
     * @param bo 项目信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ProjectInfoReq bo);

    /**
     * 修改项目信息
     *
     * @param bo 项目信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ProjectInfoReq bo);

    /**
     * 校验并批量删除项目信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验项目角色权限
     *
     * @param projectId 项目ID
     * @return 用户在项目中的角色键
     * @throws ServiceException 如果当前用户没有权限访问该项目
     */
    String checkProjectRoleKey(Long projectId);
}
