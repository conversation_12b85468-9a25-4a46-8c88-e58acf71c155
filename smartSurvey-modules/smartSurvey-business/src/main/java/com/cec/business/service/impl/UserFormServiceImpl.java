package com.cec.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.constant.CommonConstants;
import com.cec.business.domain.bo.*;
import com.cec.business.domain.enums.FormSourceTypeEnum;
import com.cec.business.domain.enums.FormStatusEnum;
import com.cec.business.domain.req.QueryFormRequest;
import com.cec.business.domain.vo.UserFormDetailVO;
import com.cec.business.domain.vo.UserFormItemCopyVO;
import com.cec.business.domain.vo.UserFormVO;
import com.cec.business.mapper.*;
import com.cec.business.service.UserFormService;
import com.cec.business.utils.FormAuthUtils;
import com.cec.business.utils.ShortIdUtils;
import com.cec.business.utils.UrlUtils;
import com.cec.common.core.domain.R;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.ValidatorUtils;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.vo.SysOssVo;
import com.cec.system.service.ISysOssService;
import com.cec.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;

/***
 * 问卷管理相关的Service实现类
 */
@Service
@RequiredArgsConstructor
public class UserFormServiceImpl extends ServiceImpl<UserFormMapper, UserFormEntity> implements UserFormService {
    private final UserFormMapper baseMapper;
    private final UserFormItemMapper userFormItemMapper;
    private final UserFormDataMapper userFormDataMapper;
    private final ISysUserService sysUserService;
    private final UserFormAuthMapper userFormAuthMapper;
    private final UserFormLogicMapper userFormLogicMapper;
    private final ISysOssService ossService;
    private final UrlUtils urlUtils;

    @Override
    public UserFormEntity getByKey(String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }
        UserFormEntity entity = getOne(Wrappers.<UserFormEntity>lambdaQuery()
            .eq(UserFormEntity::getFormKey, key)
            .eq(UserFormEntity::getDeleted, false));
        if (entity != null) {
            // 查询授权用户和部门信息
            UserFormAuthEntity auth = userFormAuthMapper.selectOne(Wrappers.<UserFormAuthEntity>lambdaQuery()
                .eq(UserFormAuthEntity::getFormKey, entity.getFormKey()));
            if (auth != null) {
                entity.setUserIdList(auth.getUserIdList());
                entity.setDeptIdList(auth.getDeptIdList());
            }
            // 查询已答题的答题数量
            entity.setAnsweredCount(getAnsweredCount(entity.getFormKey()));
            if (entity.getStatus().equals(FormStatusEnum.RELEASE)) {
                entity.setPublishUrl(urlUtils.getSurveyH5Url(entity.getFormKey()));
            }
        }
        return entity;
    }

    @Override
    public TableDataInfo<UserFormVO> selectFormPageList(QueryFormRequest param, PageQuery pageQuery) {
        Page<UserFormVO> page = (Page<UserFormVO>) baseMapper.selectVoPage(pageQuery.build(), Wrappers.<UserFormEntity>lambdaQuery()
            .likeRight(ObjectUtil.isNotEmpty(param.getName()), UserFormEntity::getName, param.getName())
            .eq(ObjectUtil.isNotNull(param.getProjectId()), UserFormEntity::getProjectId, param.getProjectId())
            .eq(ObjectUtil.isNotEmpty(param.getFormKey()), UserFormEntity::getFormKey, param.getFormKey())
            .eq(ObjectUtil.isEmpty(param.getProjectId()), UserFormEntity::getCreateBy, LoginHelper.getUserId())
            .eq(UserFormEntity::getDeleted, false)
            .eq(ObjectUtil.isNotNull(param.getStatus()) && (param.getStatus().equals(FormStatusEnum.CREATE) || param.getStatus().equals(FormStatusEnum.STOP)),
                UserFormEntity::getStatus, param.getStatus())
            .nested(ObjectUtil.isNotNull(param.getStatus()) && param.getStatus().equals(FormStatusEnum.RELEASE), i -> i
                .eq(UserFormEntity::getStatus, FormStatusEnum.RELEASE)
                .eq(UserFormEntity::getValidateEnabled, false)
                .or()
                .eq(UserFormEntity::getValidateEnabled, true)
                .ge(UserFormEntity::getValidateEndDate, DateUtil.date())
                .eq(UserFormEntity::getStatus, FormStatusEnum.RELEASE)
            )
            .nested(ObjectUtil.isNotNull(param.getStatus()) && param.getStatus().equals(FormStatusEnum.END), i -> i
                .eq(UserFormEntity::getStatus, FormStatusEnum.RELEASE)
                .eq(UserFormEntity::getValidateEnabled, true)
                .le(UserFormEntity::getValidateEndDate, DateUtil.date()))
            .orderByDesc(BaseEntity::getCreateTime)
        ).convert(item -> {
            item.setCreateByName(sysUserService.selectUserById(item.getCreateBy()).getUserName());
            item.setUpdateByName(sysUserService.selectUserById(item.getUpdateBy()).getUserName());
            item.setAnsweredCount(getAnsweredCount(item.getFormKey()));
            statusHandler(item);
            return item;
        });
        return TableDataInfo.build(page);
    }

    private void statusHandler(UserFormVO item) {
        // 问卷是收集中、开启了有效期、并且有效结束时间小于当前时间
        if (ObjectUtil.isNotNull(item.getStatus())
//            && item.getStatus().equals(FormStatusEnum.RELEASE)
            && item.getValidateEnabled()
            && ObjectUtil.isNotNull(item.getValidateEndDate())
            && item.getValidateEndDate().before(DateUtil.date())) {
            item.setStatus(FormStatusEnum.END);
        }
    }

    /***
     * 查询答题数量
     * @param key   formKey
     * @return 答题数量
     */
    public Long getAnsweredCount(String key) {
        return userFormDataMapper.selectCount(Wrappers.<UserFormDataEntity>lambdaQuery()
            .eq(UserFormDataEntity::getFormKey, key));
    }

    @Override
    public Boolean publishForm(String key) {
        FormAuthUtils.hasPermission(key);
        long count = userFormItemMapper.selectCount(Wrappers.<UserFormItemEntity>lambdaQuery()
            .eq(UserFormItemEntity::getFormKey, key));
        Assert.isTrue(count > CommonConstants.ConstantNumber.ZERO, () -> new ServiceException(MessageUtils.message("form.no.valid.items")));
        UserFormEntity userFormEntity = baseMapper.selectOne(Wrappers.<UserFormEntity>lambdaQuery()
            .eq(UserFormEntity::getFormKey, key));
        userFormEntity.setStatus(FormStatusEnum.RELEASE);
        return baseMapper.updateById(userFormEntity) > 0;
    }

    @Override
    public Boolean stopForm(String key) {
        FormAuthUtils.hasPermission(key);
        UserFormEntity entity = getByKey(key);
        entity.setStatus(entity.getStatus().equals(FormStatusEnum.RELEASE) ? FormStatusEnum.STOP : FormStatusEnum.RELEASE);
        return updateById(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<UserFormEntity> createForm(UserFormEntity form) {
        ValidatorUtils.validateEntity(form, AddGroup.class);
        form.setFormKey(ShortIdUtils.genId());
        form.setStatus(form.getStatus() == null ? FormStatusEnum.CREATE : form.getStatus());
        form.setSourceType(FormSourceTypeEnum.BLANK);
        form.setDeleted(false);
        generateQrCode(form);
        save(form);
        saveUseFormAuth(form);
        return R.ok(form);
    }

    /***
     * 生成二维码图片
     * @param form 问卷实体
     */
    private void generateQrCode(UserFormEntity form) {
        String url = urlUtils.getSurveyH5Url(form.getFormKey());
        byte[] qrCodeByte = QrCodeUtil.generatePng(url, 200, 200);
        File tempFile = null;
        try {
            tempFile = File.createTempFile("qrCode", ".png");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(qrCodeByte);
            }
            SysOssVo sysOssUploadVo = ossService.upload(tempFile);
            String qrCodeUrl = sysOssUploadVo.getUrl();
            form.setQrCodeUrl(qrCodeUrl);
            form.setPublishUrl(url);
        } catch (IOException e1) {
            throw new ServiceException(MessageUtils.message("form.create.temp.file.failed"));
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    public void saveUseFormAuth(UserFormEntity form) {
        UserFormAuthEntity auth = new UserFormAuthEntity();
        if (CollUtil.isNotEmpty(form.getUserIdList())) {
            auth.setUserIdList(form.getUserIdList());
        }
        if (CollUtil.isNotEmpty(form.getDeptIdList())) {
            auth.setDeptIdList(form.getDeptIdList());
        }
        auth.setFormKey(form.getFormKey());
        userFormAuthMapper.insert(auth);
    }

    @Override
    public Boolean logicDeleteForm(String key) {
        FormAuthUtils.hasPermission(key);
        return update(new UserFormEntity() {{
            setDeleted(Boolean.TRUE);
        }}, Wrappers.<UserFormEntity>lambdaQuery().eq(UserFormEntity::getFormKey, key));
    }

    @Override
    public Boolean deleteForm(String key) {
        FormAuthUtils.hasPermission(key);
        return remove(Wrappers.<UserFormEntity>lambdaQuery().eq(UserFormEntity::getFormKey, key));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateForm(UserFormEntity form) {
        ValidatorUtils.validateEntity(form, AddGroup.class);
        FormAuthUtils.hasPermission(form.getFormKey());
        if (CollUtil.isNotEmpty(form.getUserIdList()) || CollUtil.isNotEmpty(form.getDeptIdList())) {
            // 删除原有的权限
            userFormAuthMapper.delete(Wrappers.<UserFormAuthEntity>lambdaQuery()
                .eq(UserFormAuthEntity::getFormKey, form.getFormKey()));
            // 重新保存权限
            saveUseFormAuth(form);
        }
        return updateById(form);
    }

    @Override
    public R<UserFormDetailVO> queryFormDetails(String key) {
        UserFormEntity form = getByKey(key);
        Assert.notNull(form, () -> new ServiceException(MessageUtils.message("form.not.exists")));
        Assert.isTrue(form.getStatus().equals(FormStatusEnum.RELEASE), () -> new ServiceException(MessageUtils.message("form.status.abnormal.publish")));
        List<UserFormItemCopyVO> formItemList = userFormItemMapper.selectVoList(Wrappers.<UserFormItemEntity>lambdaQuery()
            .ne(UserFormItemEntity::getHideType, 1)
            .eq(UserFormItemEntity::getFormKey, key));
        formItemList.sort(Comparator.comparing(UserFormItemCopyVO::getSort));
        // 查询表单逻辑
        UserFormLogicEntity formLogic = userFormLogicMapper.selectOne(Wrappers.<UserFormLogicEntity>lambdaQuery()
            .eq(UserFormLogicEntity::getFormKey, key));
        return R.ok(new UserFormDetailVO(new UserFormDetailVO.UserForm(form), formItemList, formLogic));
    }
}
