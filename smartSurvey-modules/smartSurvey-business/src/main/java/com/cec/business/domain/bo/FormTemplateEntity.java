package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.vo.UserFormCopyVO;
import com.cec.business.domain.vo.UserFormItemCopyVO;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.UpdateGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 表单模板(FormTemplate)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "form_template", autoResultMap = true)
public class FormTemplateEntity extends BaseEntity {

    private Long id;

    /**
     * 模板唯一标识
     */
    @NotBlank(message = "{user.form.formKey.not.blank}", groups = {UpdateGroup.class})
    private String formKey;

    /***
     * 来源模版Id
     */
    private String sourceFormKey;

    /**
     * i1b
     * 表单名称
     */
    @NotBlank(message = "{user.form.name.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 封面图
     */
    private String coverImg;

    /**
     * 表单描述
     */
    private String description;

    /**
     * 模板内容定义
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Definition scheme;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Definition {
        private Integer formType;
        private UserFormCopyVO userForm;
        private List<UserFormItemCopyVO> formItems;
        private UserFormThemeEntity userFormTheme;
        private UserFormLogicEntity userFormLogic;
    }
}
