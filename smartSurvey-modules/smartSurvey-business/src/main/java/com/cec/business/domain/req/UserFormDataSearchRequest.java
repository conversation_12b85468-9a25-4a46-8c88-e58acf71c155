package com.cec.business.domain.req;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***
 * 问卷表单答题请求
 */
@Data
public class UserFormDataSearchRequest implements Serializable {

    /**
     * 表单key
     */
    @NotBlank(message = "{user.form.formKey.not.blank}")
    private String formKey;

    /***
     * 答题用户id,为空则表示匿名
     */
    private Long answerUserId;

    /**
     * 答题开始时间
     */
    private Date startTime;

    /**
     * 答题结束时间
     */
    private Date endTime;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 大小
     */
    private Integer pageSize = 10;

    /**
     * 被查询的字段
     */
    private String[] filterFields;

    /**
     * 数据id 集合
     */
    private List<String> dataIds;

}
