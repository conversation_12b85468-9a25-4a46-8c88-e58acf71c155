package com.cec.business.controller;

import com.cec.business.domain.req.*;
import com.cec.business.domain.vo.*;
import com.cec.business.service.ActivityApplyService;
import com.cec.business.service.ActivityService;
import com.cec.business.service.ActivitySigninService;
import com.cec.common.core.domain.R;
import com.cec.common.core.utils.HttpUtils;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 活动管理 API 接口
 **/
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/activity/")
public class ActivityController extends BaseController {
    private final ActivityService activityService;
    private final ActivitySigninService activitySigninService;
    private final ActivityApplyService activityApplyService;

    /**
     * 根据条件查询所有报名活动
     */
    @GetMapping("form/page")
    public TableDataInfo<ActivityVO> listForms(ActivityFormQuery request, PageQuery pageQuery) {
        return activityService.queryPageList(request, pageQuery);
    }

    /**
     * 创建报名活动
     */
    @Log(title = "报名活动", businessType = BusinessType.INSERT)
    @PostMapping("form/create")
    public R<ActivityVO> createForm(@RequestBody ActivityReq req) {
        return R.ok(activityService.createForm(req));
    }

    /**
     * 报名活动更新
     */
    @Log(title = "报名活动", businessType = BusinessType.UPDATE)
    @PostMapping("form/update")
    public R<Void> updateForm(@RequestBody ActivityReq req) {
        return toAjax(activityService.updateByBo(req));
    }

    /***
     * 活动发布
     */
    @Log(title = "报名活动", businessType = BusinessType.UPDATE)
    @GetMapping("publish/{id}")
    public R<Void> publishForm(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return toAjax(activityService.publishForm(id));
    }

    /***
     * 停止活动
     */
    @Log(title = "停止活动", businessType = BusinessType.UPDATE)
    @GetMapping("stop/{id}")
    public R<Void> stopForm(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return toAjax(activityService.stopForm(id));
    }

    /**
     * 删除
     */
    @Log(title = "报名活动", businessType = BusinessType.DELETE)
    @GetMapping("delete/{id}")
    public R<Void> deleteActivity(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return toAjax(activityService.deleteActivity(id));
    }

    /**
     * 查询报名活动
     */
    @GetMapping("form/{id}")
    public R<ActivityVO> queryFormById(@PathVariable @NotNull(message = "问卷key不能为空") Long id) {
        return R.ok(activityService.getById(id));
    }

    /**
     * 签到记录分页
     */
    @GetMapping("sign/page")
    public TableDataInfo<ActivitySigninVO> listSignin(ActivitySigninRequest request, PageQuery pageQuery) {
        return activitySigninService.queryPageList(request, pageQuery);
    }

    /***
     * 根据id查询签到记录
     */
    @GetMapping("sign/{id}")
    public R<ActivitySigninVO> getSignin(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(activitySigninService.queryById(id));
    }

    /**
     * 报名记录分页
     */
    @GetMapping("apply/page")
    public TableDataInfo<ActivityApplyVO> listApply(ActivityApplyRequest request, PageQuery pageQuery) {
        return activityApplyService.queryPageList(request, pageQuery);
    }

    /***
     * 根据id查询报名记录
     */
    @GetMapping("apply/{id}")
    public R<ActivityApplyVO> getApply(@PathVariable @NotNull(message = "主键不能为空") Long id) {
        return R.ok(activityApplyService.queryById(id));
    }

    /**
     * 审核报名记录
     */
    @Log(title = "报名活动-报名审核", businessType = BusinessType.UPDATE)
    @PostMapping("apply/audit")
    public R<Void> audit(@RequestBody ActivityApplyAuditRequest request) {
        return toAjax(activityApplyService.audit(request));
    }

    /***
     * H5端参与报名
     */
    @PostMapping("h5/apply/join")
    public R<Void> joinApply(@RequestBody JoinActivityRequest param) {
        return toAjax(activityApplyService.joinApply(param));
    }

    /***
     * H5端参与签到
     */
    @PostMapping("h5/signin/join")
    public R<Void> joinSignin(@RequestBody JoinActivityRequest param, HttpServletRequest request) {
        param.setSigninIp(HttpUtils.getIpAddr(request));
        return toAjax(activityApplyService.joinSignin(param));
    }

    /***
     * H5端报名活动详情
     */
    @GetMapping("h5/apply/{key}")
    public R<ActivityApplyDetailVO> getApplyDetail(@PathVariable @NotEmpty(message = "formKey不能为空") String key) {
        return R.ok(activityService.getApplyDetail(key));
    }

    /***
     * H5端签到活动详情
     */
    @GetMapping("h5/sign/{key}")
    public R<ActivitySigninDetailVO> getSigninDetail(@PathVariable @NotEmpty(message = "formKey不能为空") String key) {
        return R.ok(activityService.getSigninDetail(key));
    }

}
