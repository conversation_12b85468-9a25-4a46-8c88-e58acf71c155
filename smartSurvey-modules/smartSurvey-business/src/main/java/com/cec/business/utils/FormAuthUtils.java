package com.cec.business.utils;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.service.IProjectInfoService;
import com.cec.business.service.UserFormService;
import com.cec.business.service.impl.ProjectInfoServiceImpl;
import com.cec.common.core.constant.TenantConstants;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUserRole;
import com.cec.system.mapper.SysUserRoleMapper;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/***
 * 校验用户是否有权限操作问卷
 */
@Component
@AllArgsConstructor
public class FormAuthUtils {
    private final IProjectInfoService projectInfoService;
    private final SysUserRoleMapper sysUserRoleMapper;

    /**
     * 是否拥有表单的权限
     */
    public void hasPermission(String formKey) {
        // 是否是超级管理员
        if (isAdmin(LoginHelper.getUserId())) {
            return;
        }
        UserFormService userFormService = SpringUtils.getBean(UserFormService.class);
        UserFormEntity userFormEntity = userFormService.getByKey(formKey);
        if (ObjectUtil.isNull(userFormEntity)) {
            return;
        }
        // 校验当前用户是否具备删除权限
        String roleKey = projectInfoService.checkProjectRoleKey(userFormEntity.getProjectId());
        if (!userFormEntity.getCreateBy().equals(LoginHelper.getUserId())
            && roleKey.equals(ProjectInfoServiceImpl.ROLE_KEY_PROJECT_MEMBER)) {
            throw new ServiceException(MessageUtils.message("form.no.permission"));
        }
    }

    public boolean hasDelete(String formKey) {
        if (LoginHelper.isSuperAdmin(LoginHelper.getUserId())) {
            return true;
        }
        UserFormService userFormService = SpringUtils.getBean(UserFormService.class);
        UserFormEntity userFormEntity = userFormService.getByKey(formKey);
        if (ObjectUtil.isNull(userFormEntity)) {
            return false;
        }
        // 校验当前用户是否具备删除权限
        String roleKey = projectInfoService.checkProjectRoleKey(userFormEntity.getProjectId());
        return userFormEntity.getCreateBy().equals(LoginHelper.getUserId())
            || !roleKey.equals(ProjectInfoServiceImpl.ROLE_KEY_PROJECT_MEMBER);
    }



    public boolean isAdmin(Long userId) {
        if (LoginHelper.isSuperAdmin(userId)) {
            return true;
        }

        return sysUserRoleMapper.exists(Wrappers.<SysUserRole>lambdaQuery()
            .eq(SysUserRole::getUserId, userId)
            .eq(SysUserRole::getRoleId, TenantConstants.SUPER_ADMIN_ID));
    }
}
