package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.BooleanTypeHandler;


/**
 * 表单表单项(UserFormTheme)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName(value = "user_form_theme", autoResultMap = true)
public class UserFormThemeEntity extends BaseEntity {

    private Long id;

    /**
     * 表单key
     */
    @NotBlank(message = "{user.form.formKey.not.blank}")
    private String formKey;

    /**
     * logo图片
     */
    private String logoImg;

    /**
     * logo位置
     */
    private String logoPosition;

    /**
     * 头部图片
     */
    private String headImgUrl;


    /**
     * 主题颜色
     */
    private String themeColor;

    /**
     * 提交按钮文字
     */
    private String submitBtnText;


    /**
     * 背景
     */
    private String backgroundColor;

    /**
     * 背景
     */
    private String backgroundImg;
    /**
     * 是否显示标题
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showTitle;
    /**
     * 是否显示描述语
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showDescribe;
    /**
     * 显示序号
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showNumber;

    /**
     * 显示提交按钮
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showSubmitBtn;

}
