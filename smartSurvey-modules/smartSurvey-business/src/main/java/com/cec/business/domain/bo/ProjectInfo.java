package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 项目信息对象 project_info
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("project_info")
@Accessors(chain = true)
public class ProjectInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 任务描述
     */
    private String content;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建用户账号
     */
    private String createStaffNum;

    /**
     * 创建用户名称
     */
    private String createStaffName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;


}
