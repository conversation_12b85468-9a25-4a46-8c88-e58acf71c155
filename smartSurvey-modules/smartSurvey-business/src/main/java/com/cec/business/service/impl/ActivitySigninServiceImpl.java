package com.cec.business.service.impl;

import com.cec.business.domain.bo.ActivitySigninEntity;
import com.cec.business.domain.req.ActivitySigninRequest;
import com.cec.business.domain.vo.ActivitySigninVO;
import com.cec.business.mapper.ActivitySigninMapper;
import com.cec.business.service.ActivitySigninService;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.SysUser;
import com.cec.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 活动签到记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ActivitySigninServiceImpl implements ActivitySigninService {
    private final ActivitySigninMapper baseMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 查询活动签到记录
     *
     * @param id 主键
     * @return 活动签到记录
     */
    @Override
    public ActivitySigninVO queryById(Long id) {
        ActivitySigninVO vo = baseMapper.selectVoById(id);
        if (vo != null) {
            SysUser user = sysUserMapper.selectById(vo.getUserId());
            if (user != null) {
                vo.setStaffNum(user.getStaffNum());
                vo.setStaffName(user.getStaffName());
            }
        }
        return vo;
    }

    /**
     * 分页查询活动签到记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动签到记录分页列表
     */
    @Override
    public TableDataInfo<ActivitySigninVO> queryPageList(ActivitySigninRequest bo, PageQuery pageQuery) {
        return TableDataInfo.build(baseMapper.selectSigninVoPage(pageQuery.build(), bo));
    }

    /**
     * 修改活动签到记录
     *
     * @param bo 活动签到记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ActivitySigninEntity bo) {
        ActivitySigninEntity update = MapstructUtils.convert(bo, ActivitySigninEntity.class);
        return baseMapper.updateById(update) > 0;
    }
}
