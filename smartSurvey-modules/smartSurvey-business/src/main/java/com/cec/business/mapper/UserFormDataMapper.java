package com.cec.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.bo.UserFormDataEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 表单表单数据(FormResult)表数据库访问层
 */
public interface UserFormDataMapper extends BaseMapper<UserFormDataEntity> {

    /**
     * 查询表单数据条数
     *
     * @param sql
     * @return
     */
    @Select("${sql}")
    Long selectCountBySql(@Param("sql") String sql);

    /**
     * 查询表单数据
     *
     * @param sql sql
     * @return
     */
    @Results(id = "queryRowsResultMap",
        value = {
            @Result(property = "originalData", column = "original_data", typeHandler = JacksonTypeHandler.class),
        })
    @Select("${sql}")
    List<UserFormDataEntity> selectRowsBySql(@Param("sql") String sql);
}
