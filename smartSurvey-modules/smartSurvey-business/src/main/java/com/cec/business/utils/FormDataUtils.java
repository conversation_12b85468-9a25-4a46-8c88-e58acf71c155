package com.cec.business.utils;

import cn.hutool.core.collection.CollUtil;
import com.cec.business.domain.enums.FormItemTypeEnum;
import com.cec.business.domain.req.UserFormDataSearchRequest;
import com.cec.business.domain.vo.FormDataTableVO;
import com.cec.business.service.data.FormDataBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description : 表单收集结果工具类
 * @create :  2021/08/18 18:17
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class FormDataUtils {
    private final FormDataBaseService formDataBaseService;

    /**
     * 特殊字段 会多出一个xxx label字段存放显示值 默认字段存放原始值
     */
    public static final List<FormItemTypeEnum> specialFields = CollUtil.newArrayList(FormItemTypeEnum.SELECT,
        FormItemTypeEnum.IMAGE_SELECT,
        FormItemTypeEnum.CHECKBOX,
        FormItemTypeEnum.RADIO,
        FormItemTypeEnum.CASCADER);

    /**
     * 查询表单分页数据
     *
     * @param request 查询参数
     * @return 表单分页数据
     */
    public FormDataTableVO search(UserFormDataSearchRequest request) {
        return formDataBaseService.search(request);
    }

}
