package com.cec.business.service;

import com.cec.business.domain.bo.ActivityEntity;
import com.cec.business.domain.req.ActivityFormQuery;
import com.cec.business.domain.req.ActivityReq;
import com.cec.business.domain.vo.ActivityApplyDetailVO;
import com.cec.business.domain.vo.ActivitySigninDetailVO;
import com.cec.business.domain.vo.ActivityVO;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

public interface ActivityService {

    /**
     * 查询活动主
     *
     * @param id 主键
     * @return 活动主
     */
    ActivityVO queryById(Long id);

    /**
     * 分页查询活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动主分页列表
     */
    TableDataInfo<ActivityVO> queryPageList(ActivityFormQuery bo, PageQuery pageQuery);

    /**
     * 获取活动角色权限
     *
     * @param activityId 项目ID
     * @return 用户在项目中的角色键
     * @throws ServiceException 如果当前用户没有权限访问该项目
     */
    String getActivityRoleKey(Long activityId);

    /**
     * 查询符合条件的活动主列表
     *
     * @param bo 查询条件
     * @return 活动主列表
     */
    List<ActivityVO> queryList(ActivityFormQuery bo);

    /**
     * 新增活动主
     *
     * @param bo 活动主
     * @return 是否新增成功
     */
    Boolean insertByBo(ActivityEntity bo);

    /**
     * 修改活动主
     *
     * @param req 活动主
     * @return 是否修改成功
     */
    Boolean updateByBo(ActivityReq req);

    /**
     * 校验并批量删除活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /***
     * 创建活动表单，并返回表单id
     * @param form
     * @return
     */
    ActivityVO createForm(ActivityReq form);

    /***
     * 根据key查询活动信息
     * @param key
     * @return
     */
    ActivityEntity getByKey(String key);

    /***
     * 根据key查询活动信息
     * @param id
     * @return
     */
    ActivityVO getById(Long id);

    /***
     * 根据key查询活动信息
     * @param key     活动key
     * @return 活动信息
     */
    ActivityApplyDetailVO getApplyDetail(String key);

    /***
     * 根据key查询签到信息
     * @param key     活动key
     * @return 活动信息
     */
    ActivitySigninDetailVO getSigninDetail(String key);

    /***
     * 根据id删除活动信息
     * @param id     活动id
     */
    Boolean deleteActivity(Long id);

    /***
     * 活动发布
     * @param id
     */
    Boolean publishForm(Long id);

    /***
     * 停止活动
     * @param id
     * @return
     */
    Boolean stopForm(Long id);
}
