package com.cec.business.domain.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * @description : 排序表单项
 * @create : 2020-11-20 10:14
 **/
@Data
public class SortFormItemRequest {
    /**
     * 表单Id
     */
    @NotNull(message = "{validation.form.key.request.error}")
    private String formKey;

    /**
     * 原始排序号
     */
    private Long beforePosition;

    /**
     * 目标排序号
     */
    private Long afterPosition;

    /**
     * 表单itemId
     */
    @NotBlank(message = "formItemId请求异常")
    private String formItemId;

}
