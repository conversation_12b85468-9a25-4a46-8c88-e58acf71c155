package com.cec.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cec.business.domain.bo.UserFormItemEntity;
import com.cec.business.domain.req.QueryFormItemRequest;
import com.cec.business.domain.req.SortFormItemRequest;
import com.cec.business.domain.vo.FormFieldVO;
import com.cec.business.domain.vo.OperateFormItemVO;

import java.util.List;

/**
 * 表单表单项(FormItem)表服务接口
 *
 * @since 2020-11-19 10:49:17
 */
public interface UserFormItemService extends IService<UserFormItemEntity> {


    /**
     * 根据key查询
     *
     * @param key key
     * @return 表单表单项
     */
    List<UserFormItemEntity> listByFormKey(String key);

    /**
     * 查询自定义字段
     *
     * @param formKey 表单key
     * @return 自定义字段
     */
    List<FormFieldVO> listFormFields(String formKey);


    /**
     * 查询最后一个字段的排序值
     *
     * @param formKey 表单key
     * @return 排序值
     */
    Long getLastItemSort(String formKey);

    /**
     * 检查字段是否是需要特殊处理字段 比如随机编号
     *
     * @param userFormItemEntity 字段
     * @return true 需要特殊处理
     */
    Boolean isSpecialTypeItem(UserFormItemEntity userFormItemEntity);


    /***
     * 查询表单表单项
     * @param request   查询条件
     * @return 表单表单项列表
     */
    List<UserFormItemEntity> queryFormItems(QueryFormItemRequest request);

    /***
     * 批量项目表单项创建
     * @param itemEntityList    表单表单项列表
     * @return
     */
    Boolean batchCreateFormItem(List<UserFormItemEntity> itemEntityList);

    /***
     * 修改表单表单项
     * @param request    表单项信息
     * @return 是否成功
     */
    Boolean updateFormItem(UserFormItemEntity request);

    /***
     * 删除表单表单项
     * @return 是否成功
     */
    Boolean deleteFormItem(String key);

    /***
     * 排序表单表单项
     * @param request   表单项信息
     * @return 操作结果
     */
    OperateFormItemVO sortFormItem(SortFormItemRequest request);

}
