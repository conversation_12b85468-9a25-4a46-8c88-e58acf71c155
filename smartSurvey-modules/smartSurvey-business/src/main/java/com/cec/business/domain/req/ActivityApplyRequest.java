package com.cec.business.domain.req;

import com.cec.business.domain.enums.ActivityApplyAuditStateEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ActivityApplyRequest implements Serializable {

    /***
     * 活动id
     */
    private Long activityId;

    /***
     * 员工编码
     */
    private String staffNum;

    /***
     * 审核状态
     */
    private ActivityApplyAuditStateEnum auditState;

}
