package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 单链接扩展值对象 user_form_link_ext
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("user_form_link_ext")
public class UserFormLinkExtEntity extends BaseEntity {

    private Long id;

    /**
     * 表单唯一标识
     */
    @NotBlank
    private String formKey;

    /**
     * 扩展值
     */
    @NotBlank
    private String extValue;


}
