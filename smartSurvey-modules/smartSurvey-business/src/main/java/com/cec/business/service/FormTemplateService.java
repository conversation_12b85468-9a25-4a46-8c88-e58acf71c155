package com.cec.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cec.business.domain.bo.FormTemplateEntity;
import com.cec.business.domain.req.QueryFormTemplateRequest;
import com.cec.business.domain.vo.FormTemplateVO;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;


/**
 * 表单表(FormTemplate)表服务接口
 *
 * @since 2021-01-06 10:43:01
 */
public interface FormTemplateService extends IService<FormTemplateEntity> {


    /**
     * 根據key獲取
     *
     * @param key 唯一标识
     * @return 模板
     */
    FormTemplateEntity getByKey(String key);


    /**
     * 创建模板
     *
     * @param formTemplate 模板
     * @return 模板
     */
    FormTemplateEntity createFormTemplate(FormTemplateEntity formTemplate);

    /**
     * 根据模板创建表单
     *
     * @param formTemplate 模板
     * @return 表单
     */
//    UserFormEntity createFormByTemplate(FormTemplateEntity formTemplate);

    /***
     * 分页查询项目模板
     * @param request
     * @param pageQuery
     * @return
     */
    TableDataInfo<FormTemplateVO> selectFormTemplatePageList(QueryFormTemplateRequest request, PageQuery pageQuery);

    /***
     * 删除模版
     * @param key   模版key
     */
    void deleteByKey(String key);

    /***
     * 获取模板定义
     * @param key   模版key
     * @return 模板定义
     */
    FormTemplateEntity.Definition getTemplateDefinition(String key);
}
