package com.cec.business.domain.vo;

import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问卷的文件数据统计，适用于upload
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormDataStatFileVO {

    /**
     * 填写率（带百分号，如：85.5%）
     */
    private String fillRate;

    /**
     * 填写数量
     */
    private Integer fillCount;

    /**
     * 文件总数量
     */
    private Integer totalFileCount;

    /**
     * 分页的文件列表数据
     */
    private TableDataInfo<FormDataStatFileItemVO> tableDataInfo;

    /**
     * 文件统计项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDataStatFileItemVO {

        /**
         * 记录ID
         */
        private Long id;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件URL
         */
        private String url;

        /**
         * OSS文件ID
         */
        private String ossId;

        /**
         * 文件大小（字节）
         */
        private Long size;
    }
}
