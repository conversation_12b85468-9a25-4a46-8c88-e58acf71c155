package com.cec.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.business.domain.bo.UserFormThemeEntity;
import com.cec.business.mapper.UserFormThemeMapper;
import com.cec.business.service.UserFormThemeService;
import org.springframework.stereotype.Service;

@Service
public class UserFormThemeServiceImpl extends ServiceImpl<UserFormThemeMapper, UserFormThemeEntity> implements UserFormThemeService {
    @Override
    public UserFormThemeEntity getByKey(String key) {
        return this.getOne(Wrappers.<UserFormThemeEntity>lambdaQuery().eq(UserFormThemeEntity::getFormKey, key));
    }
}
