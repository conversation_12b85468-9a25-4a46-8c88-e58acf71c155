package com.cec.business.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.cec.business.domain.bo.ActivitySigninEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动签到记录视图对象 activity_signin
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActivitySigninEntity.class)
public class ActivitySigninVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 签到ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 报名ID
     */
    private Long applyId;

    /**
     * 签到IP
     */
    private String signinIp;

    /***
     * 签到用户id
     */
    private String userId;

    /**
     * 问卷数据ID(关联user_form_data)
     */
    private Long formDataId;

    /***
     * 签到时间
     */
    private Date createTime;

    /***
     * 员工编号
     */
    private String staffNum;

    /***
     * 员工姓名
     */
    private String staffName;

}
