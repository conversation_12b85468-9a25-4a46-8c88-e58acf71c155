package com.cec.business.domain.req;


import com.cec.business.domain.bo.ProjectMemberRole;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目成员角色业务对象 project_member_role
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectMemberRole.class, reverseConvertGenerate = false)
public class ProjectMemberRoleReq extends BaseEntity {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户ID
     */
    private Long deptId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;


}
