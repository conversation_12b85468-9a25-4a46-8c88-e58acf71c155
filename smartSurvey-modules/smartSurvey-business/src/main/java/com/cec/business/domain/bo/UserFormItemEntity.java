package com.cec.business.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.utils.HtmlUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.UpdateGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.util.List;
import java.util.Map;

/**
 * 表单表单项(FormItem)表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "user_form_item", autoResultMap = true)
public class UserFormItemEntity extends BaseEntity {

    private Long id;
    /**
     * 表单Id
     */
    @NotNull(message = "{user.form.formKey.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private String formKey;
    /**
     * 表单项Id 类型  + 时间戳
     */
    @NotNull(message = "{user.form.item.formItemId.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private String formItemId;

    /**
     * 表单项类型
     */
    @NotNull(message = "{user.form.item.type.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private String type;

    /**
     * 表单项标题
     */
    @NotNull(message = "{user.form.item.label.not.blank}", groups = {AddGroup.class, UpdateGroup.class})
    private String label;


    /**
     * 展示类型组件 只在表单填写页查询到
     */
    @TableField(value = "is_display_type", typeHandler = BooleanTypeHandler.class)
    private Boolean displayType;

    /**
     * 隐藏类型组件 在表单填写页面无法查看到
     */
    @TableField(value = "is_hide_type", typeHandler = BooleanTypeHandler.class)
    private Boolean hideType;

    /**
     * 需要在入库前特殊处理的组件 比如随机编码等 验重
     */
    @TableField(value = "is_special_type", typeHandler = BooleanTypeHandler.class)
    private Boolean specialType;
    /**
     * 是否显示标签
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showLabel;

    /**
     * 表单项默认值
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String defaultValue;


    /**
     * 是否必填
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean required;
    /**
     * 输入型提示文字
     */
    private String placeholder;
    /**
     * 排序
     */
    private Long sort;

    /**
     * 栅格宽度
     */
    private int span;

    /**
     * 扩展字段 表单项独有字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> scheme;

    /**
     * 正则表达式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> regList;


    /**
     * 去除html格式
     */
    public String getTextLabel() {
        return HtmlUtils.cleanHtmlTag(this.label);
    }


}
