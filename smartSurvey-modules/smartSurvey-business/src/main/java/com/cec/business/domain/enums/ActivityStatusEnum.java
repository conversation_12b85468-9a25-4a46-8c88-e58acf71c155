package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cec.business.domain.IDictEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动状态
 **/
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ActivityStatusEnum implements IDictEnum<Integer> {
    CREATE(1, "待发布"),
    RELEASE(2, "进行中"),
    END(3, "已结束");

    @EnumValue
    private final Integer value;

    private final String desc;

    /**
     * 根据code获取枚举
     */
    @JsonCreator
    public static ActivityStatusEnum findByValue(Integer value) {
        for (ActivityStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    public static ActivityStatusEnum fromString(String value) {
        if (value == null) {
            return null;
        }
        try {
            Integer valueInt = Integer.parseInt(value);
            return findByValue(valueInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
