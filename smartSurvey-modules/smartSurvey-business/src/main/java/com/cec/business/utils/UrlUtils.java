package com.cec.business.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class UrlUtils {

    @Value("${h5.host}")
    private String domainUrl;

    /***
     * 生成问卷的H5地址
     * @param formKey
     * @return
     */
    public String getSurveyH5Url(String formKey) {
        if (!domainUrl.endsWith("/")) {
            domainUrl += "/";
        }
        return domainUrl + "#/pages/s/s?k=" + formKey;
    }

    public String getSurveyActivityUrl(String formKey, String prifix) {
        if (!domainUrl.endsWith("/")) {
            domainUrl += "/";
        }
        return domainUrl + "#/pages/a/a?t=" + prifix + "&k=" + formKey;
    }


}
