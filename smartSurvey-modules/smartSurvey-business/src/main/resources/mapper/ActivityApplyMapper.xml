<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cec.business.mapper.ActivityApplyMapper">
    <select id="selectApplyVoPage" resultType="com.cec.business.domain.vo.ActivityApplyVO">
        SELECT
            s.id,
            s.activity_id,
            s.audit_state,
            s.user_id,
            s.audit_remark,
            s.create_time,
            s.audit_time,
            s.update_time,
            u.staff_num,
            u.staff_name,
            u_update.staff_name as 'audit_name'
        FROM
            activity_apply s
            LEFT JOIN
            sys_user u ON s.user_id = u.user_id
            LEFT JOIN
            sys_user u_update ON s.update_by = u_update.user_id
        <where>
            <if test="bo.activityId != null">
                AND s.activity_id = #{bo.activityId}
            </if>
            <if test="bo.staffNum != null">
                AND u.staff_num = #{bo.staffNum}
            </if>
            <if test="bo.auditState != null">
                AND s.audit_state = #{bo.auditState}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>
</mapper>
