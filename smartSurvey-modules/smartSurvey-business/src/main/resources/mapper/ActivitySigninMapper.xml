<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cec.business.mapper.ActivitySigninMapper">


    <select id="selectSigninVoPage" resultType="com.cec.business.domain.vo.ActivitySigninVO">

        SELECT
            s.id,
            s.activity_id,
            s.apply_id,
            s.signin_ip,
            s.user_id,
            s.form_data_id,
            s.create_time,
            u.staff_num,
            u.staff_name
        FROM
            activity_signin s
            LEFT JOIN
            sys_user u ON s.user_id = u.user_id
        <where>
            <if test="bo.activityId != null">
                AND s.activity_id = #{bo.activityId}
            </if>
            <if test="bo.staffNum != null">
                AND u.staff_num = #{bo.staffNum}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>
</mapper>
