package com.cec.generator.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cec.common.mybatis.core.mapper.BaseMapperPlus;
import com.cec.generator.domain.GenTableColumn;

/**
 * 业务字段 数据层
 *
 * <AUTHOR> Li
 */
@InterceptorIgnore(dataPermission = "true", tenantLine = "true")
public interface GenTableColumnMapper extends BaseMapperPlus<GenTableColumn, GenTableColumn> {

}
