package com.cec.system.service.impl;

import com.cec.common.core.constant.TenantConstants;
import com.cec.common.core.service.PermissionService;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.service.ISysMenuService;
import com.cec.system.service.ISysPermissionService;
import com.cec.system.service.ISysRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysPermissionServiceImpl implements ISysPermissionService, PermissionService {

    private final ISysRoleService roleService;
    private final ISysMenuService menuService;

    /**
     * 获取角色数据权限
     *
     * @param userId 用户id
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermission(Long userId) {
        Set<String> roles = new HashSet<>();
        Set<String> roleSet = roleService.selectRolePermissionByUserId(userId);
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId) || LoginHelper.isSuperAdmin(roleSet)) {
            roles.add(TenantConstants.SUPER_ADMIN_ROLE_KEY);
        } else {
            roles.addAll(roleSet);
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param userId 用户id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(Long userId) {
        Set<String> perms = new HashSet<>();
        Set<String> roleSet = roleService.selectRolePermissionByUserId(userId);
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId) || LoginHelper.isSuperAdmin(roleSet)) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(userId));
        }
        return perms;
    }
}
