package com.cec.system.domain.vo;

import com.cec.common.sensitive.annotation.Sensitive;
import com.cec.common.sensitive.core.SensitiveStrategy;
import com.cec.common.translation.annotation.Translation;
import com.cec.common.translation.constant.TransConstant;
import com.cec.system.domain.SysUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUser.class)
public class SysUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 员工编号
     */
    private String staffNum;

    /**
     * 员工英文名
     */
    private String staffName;

    /**
     * 员工中文名
     */
    private String staffNameCn;

    /**
     * 组织名称
     */
    private String orgChartName;

    /**
     * 上级用户ID
     */
    private String parentId;

    /**
     * 上级员工编号
     */
    private String parentNum;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 入职日期
     */
    private Date joinDate;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 用户邮箱
     */
    @Sensitive(strategy = SensitiveStrategy.EMAIL, perms = "system:user:edit")
    private String email;

    /**
     * 手机号码
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE, perms = "system:user:edit")
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 地区
     */
    private String regionName;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 等级
     */
    private String gradeOriginal;

    /**
     * 业务线
     */
    private Integer bizLine;

    /**
     * 修改时间(GMT8)
     */
    private Date gmt8Modified;

    /**
     * 公司ID
     */
    private String kdCompanyId;

    /**
     * 公司名称
     */
    private String kdCompanyName;

    /**
     * 部门名称
     */
    private String kdDeptName;

    /**
     * 科室名称
     */
    private String kdSectionName;

    /**
     * 财务部门
     */
    private String financeDepartment;

    /**
     * 是否是父节点
     */
    private Boolean isParent;

    /**
     * 密码
     */
    @JsonIgnore
    @JsonProperty
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门名
     */
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "deptId")
    private String deptName;

    /**
     * 角色对象
     */
    private List<SysRoleVo> roles;

    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    /**
     * 数据权限 当前角色ID
     */
    private Long roleId;

}
