package com.cec.system.domain.bo;

import com.cec.common.core.constant.RegexConstants;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.SysDictType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典类型业务对象 sys_dict_type
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysDictType.class, reverseConvertGenerate = false)
public class SysDictTypeBo extends BaseEntity {

    /**
     * 字典主键
     */
    private Long dictId;

    /**
     * 字典名称
     */
    @NotBlank(message = "{dict.type.name.not.blank}")
    @Size(min = 0, max = 100, message = "{dict.type.name.size}")
    private String dictName;

    /**
     * 字典类型
     */
    @NotBlank(message = "{dict.type.type.not.blank}")
    @Size(min = 0, max = 100, message = "{dict.type.type.size}")
    @Pattern(regexp = RegexConstants.DICTIONARY_TYPE, message = "{dict.type.type.pattern}")
    private String dictType;

    /**
     * 备注
     */
    @Size(min = 0, max = 500, message = "{dict.type.remark.size}")
    private String remark;


}
