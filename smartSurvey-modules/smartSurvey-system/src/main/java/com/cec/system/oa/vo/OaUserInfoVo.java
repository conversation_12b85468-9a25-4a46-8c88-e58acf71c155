package com.cec.system.oa.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户数据")
@Data
public class OaUserInfoVo implements java.io.Serializable {
    @Schema(description = "ID")
    private String id;
    @Schema(description = "职工编号")
    private String staffNum;
    @Schema(description = "职工账号")
    private String staffAccount;
    @Schema(description = "职工姓名")
    private String staffName;
    @Schema(description = "职工头衔")
    private String jobTitle;
    @Schema(description = "手机号")
    private String mobilePhone;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "地区 id")
    private String regionId;
    @Schema(description = "区域名称")
    private String regionName;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "组织机构 Id")
    private String orgId;
    @Schema(description = "组织机构名称")
    private String orgChartName;
    @Schema(description = "在职/离职标识: 514：离职；66048/512：在职")
    private String versionId;
    @Schema(description = "业务线（0：CPC 1：CEC）")
    private Integer bizLine;
    @Schema(description = "创建时间")
    private String gmt8Create;
    @Schema(description = "更新时间")
    private String gmt8Modified;
}
