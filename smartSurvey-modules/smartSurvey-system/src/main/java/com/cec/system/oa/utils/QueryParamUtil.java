package com.cec.system.oa.utils;

import cn.hutool.core.util.StrUtil;
import com.cec.common.core.exception.ServiceException;
import com.cec.system.oa.dto.OaUserPageQry;
import com.cec.system.oa.dto.OrderItem;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class QueryParamUtil {
    public static final int MAX_PARAM_LENGTH = 500;

    public static String convertPostParamsToGetParams(OaUserPageQry qry) {
        if (qry == null) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        boolean first = true;

        // Handle simple fields
        if (qry.getId() != null) {
            result.append("?id=").append(encodeValue(qry.getId()));
            first = false;
        }

        if (qry.getStaffNum() != null) {
            appendParam(result, "staffNum", qry.getStaffNum(), first);
            first = false;
        }

        if (qry.getStaffAccount() != null) {
            appendParam(result, "staffAccount", qry.getStaffAccount(), first);
            first = false;
        }

        if (qry.getStaffName() != null) {
            appendParam(result, "staffName", qry.getStaffName(), first);
            first = false;
        }

        if (qry.getOrgChartName() != null) {
            appendParam(result, "orgChartName", qry.getOrgChartName(), first);
            first = false;
        }

        if (qry.getMobilePhone() != null) {
            appendParam(result, "mobilePhone", qry.getMobilePhone(), first);
            first = false;
        }

        if (qry.getEmail() != null) {
            appendParam(result, "email", qry.getEmail(), first);
            first = false;
        }

        if (qry.getHasLeave() != null) {
            appendParam(result, "hasLeave", qry.getHasLeave(), first);
            first = false;
        }

        if (qry.getPageNum() != null) {
            appendParam(result, "pageNum", qry.getPageNum().toString(), first);
            first = false;
        }

        if (qry.getPageSize() != null) {
            appendParam(result, "pageSize", qry.getPageSize().toString(), first);
            first = false;
        }

        // Handle orderItemList
        if (qry.getOrderItemList() != null && !qry.getOrderItemList().isEmpty()) {
            for (int i = 0; i < qry.getOrderItemList().size(); i++) {
                OrderItem item = qry.getOrderItemList().get(i);
                appendParam(result, "orderItemList[" + i + "].asc", String.valueOf(item.asc), first);
                first = false;
                appendParam(result, "orderItemList[" + i + "].column", item.column, false);
            }
        }

        return result.toString();
    }

    private static void appendParam(StringBuilder result, String key, String value, boolean first) {
        if (StrUtil.isNotEmpty(value) && value.length() > MAX_PARAM_LENGTH) {
            throw new ServiceException("参数长度超出限制");
        }
        result.append(first ? "" : "&")
                .append(encodeValue(key))
                .append("=")
                .append(encodeValue(value));
    }

    private static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Error encoding parameter", e);
        }
    }


    public static void main(String[] args) {
        OaUserPageQry qry = new OaUserPageQry();
        qry.setPageNum(1);
        qry.setPageSize(10);
        qry.setHasLeave("0");
        OrderItem orderItem = new OrderItem();
        orderItem.setAsc(false);
        orderItem.setColumn("id");

        qry.setOrderItemList(List.of(orderItem));
        String queryString = QueryParamUtil.convertPostParamsToGetParams(qry);
        System.out.println(queryString);


    }
}
