package com.cec.system.domain.bo;

import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.SysDictData;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据业务对象 sys_dict_data
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysDictData.class, reverseConvertGenerate = false)
public class SysDictDataBo extends BaseEntity {

    /**
     * 字典编码
     */
    private Long dictCode;

    /**
     * 字典排序
     */
    private Integer dictSort;

    /**
     * 字典标签
     */
    @NotBlank(message = "{dict.data.label.not.blank}")
    @Size(min = 0, max = 100, message = "{dict.data.label.size}")
    private String dictLabel;

    /**
     * 字典键值
     */
    @NotBlank(message = "{dict.data.value.not.blank}")
    @Size(min = 0, max = 100, message = "{dict.data.value.size}")
    private String dictValue;

    /**
     * 字典类型
     */
    @NotBlank(message = "{dict.data.type.not.blank}")
    @Size(min = 0, max = 100, message = "{dict.data.type.size}")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Size(min = 0, max = 100, message = "{dict.data.css.class.size}")
    private String cssClass;

    /**
     * 表格回显样式
     */
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    private String isDefault;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 备注
     */
    @Size(min = 0, max = 500, message = "{dict.data.remark.size}")
    private String remark;

}
