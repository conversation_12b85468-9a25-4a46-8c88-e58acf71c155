package com.cec.system.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.core.domain.R;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.redis.utils.CacheUtils;
import com.cec.system.domain.SysDept;
import com.cec.system.domain.SysUser;
import com.cec.system.mapper.SysDeptMapper;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.oa.api.OaServiceApi;
import com.cec.system.oa.dto.OaUserPageQry;
import com.cec.system.oa.dto.OrderItem;
import com.cec.system.oa.resp.OaUserInfoResult;
import com.cec.system.oa.resp.OrgChartInfo;
import com.cec.system.oa.resp.OrgUserTreeResponse;
import com.cec.system.oa.resp.SysUserTreeInfo;
import com.cec.system.oa.vo.OaUserInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Slf4j
@Service
public class SyncUserService implements ApplicationRunner {

    private final OaServiceApi oaServiceApi;
    private final SysDeptMapper sysDeptMapper;
    private final SysUserMapper sysUserMapper;
    private final RedissonClient redissonClient;
    private final ISysUserService sysUserService;

    // 系统启动标志key
    private static final String SYSTEM_STARTED_KEY = "system_started_flag";
    private static final String CACHE_NAME = "system";
    // 分布式锁key
    private static final String SYNC_LOCK_KEY = "lock:sync_user_init";

    /**
     * 在所有Spring Bean初始化完成后执行
     * ApplicationRunner接口实现，确保所有依赖已就绪
     */
    @Override
    public void run(ApplicationArguments args) {
        RLock lock = redissonClient.getLock(SYNC_LOCK_KEY);
        boolean isLocked = false;

        try {
            // 尝试获取分布式锁，最多等待3秒，锁定10分钟
            log.debug("尝试获取初始化同步的分布式锁");
            isLocked = lock.tryLock(3, TimeUnit.SECONDS);

            if (isLocked) {
                log.info("成功获取初始化同步的分布式锁");
                Boolean systemStarted = CacheUtils.get(CACHE_NAME, SYSTEM_STARTED_KEY);
                if (systemStarted == null || !systemStarted) {
                    log.info("系统首次启动，执行全量用户数据同步");
                    // 执行全量同步，忽略Redis中的同步标志
                    SpringUtils.getBean(SyncUserService.class).syncUser();
                    // 设置系统启动标志
                    CacheUtils.put(CACHE_NAME, SYSTEM_STARTED_KEY, true);
                    log.info("系统启动全量同步完成，已设置系统启动标志");
                } else {
                    log.info("系统非首次启动，跳过全量同步，等待定时任务执行增量同步");
                }
            } else {
                log.info("无法获取初始化同步的分布式锁，可能其他实例正在执行同步");
            }
        } catch (Exception e) {
            log.error("系统启动同步初始化异常", e);
        } finally {
            // 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("已释放初始化同步的分布式锁");
            }
        }
    }

    /***
     * 提供给管理后台手工同步用户的接口
     */
    public Boolean handlerSyncUser() {
        RLock lock = redissonClient.getLock(SYNC_LOCK_KEY);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!isLocked) {
                throw new ServiceException("正在执行同步");
            }
            SpringUtils.getBean(SyncUserService.class).syncUser();
            return true;
        } catch (Exception e) {
            log.error("手工同步用户异常", e);
            return false;
        } finally {
            // 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 同步OA系统用户数据到本地系统
     *
     * @return 同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    public R<String> syncUser() {
        log.info("开始同步OA用户数据");
        try {
            // 获取OA系统的组织架构和用户数据
            OrgUserTreeResponse orgUserTree = oaServiceApi.getOrgUserTree(Collections.emptyList(), Collections.emptyList());
            if (orgUserTree == null || !orgUserTree.getSuccess() || orgUserTree.getData() == null) {
                log.error("获取OA用户数据失败: {}", orgUserTree != null ? orgUserTree.getMsg() : "返回为空");
                return R.fail("获取OA用户数据失败");
            }

            // 同步部门数据
            syncDepartments(orgUserTree.getData());

            // 同步用户数据
            syncUsers(orgUserTree.getData());

            // 删除离职用户
            deleteLeaveUser();

            return R.ok("同步成功");
        } catch (Exception e) {
            log.error("同步OA用户数据异常", e);
            return R.fail("同步异常: " + e.getMessage());
        }
    }

    /**
     * 同步部门数据
     *
     * @param orgChartInfoList 组织架构信息列表
     */
    private void syncDepartments(List<OrgChartInfo> orgChartInfoList) {
        if (orgChartInfoList == null || orgChartInfoList.isEmpty()) {
            return;
        }

        log.info("开始同步部门数据，部门数量: {}", orgChartInfoList.size());

        for (OrgChartInfo orgInfo : orgChartInfoList) {

            SysDept existDept = sysDeptMapper.selectOne(
                new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId, Long.valueOf(orgInfo.getId()))
            );
            if (ObjectUtils.isNotNull(existDept)) {
                // 部门已存在，执行更新
                updateDepartment(existDept, orgInfo);
            } else {
                // 首次同步或部门不存在，执行插入
                insertDepartment(orgInfo);
            }
        }
    }

    /**
     * 更新部门信息
     *
     * @param existDept 已存在的部门对象
     * @param orgInfo   组织架构信息
     */
    private void updateDepartment(SysDept existDept, OrgChartInfo orgInfo) {
        existDept.setDeptName(orgInfo.getOrgChartName());
        existDept.setShortForm(orgInfo.getShortForm());
        existDept.setLevelId(orgInfo.getLevelId());
        existDept.setVersionId(orgInfo.getVersionId());
        existDept.setLevelHeadIds(orgInfo.getLevelHeadIds());

        if (StringUtils.isNotBlank(orgInfo.getParentId())) {
            existDept.setParentId(Long.valueOf(orgInfo.getParentId()));
        }

        sysDeptMapper.updateById(existDept);
        log.debug("更新部门: {}", existDept.getDeptName());
    }

    /**
     * 插入新部门
     *
     * @param orgInfo 组织架构信息
     */
    private void insertDepartment(OrgChartInfo orgInfo) {
        SysDept dept = new SysDept();
        dept.setDeptId(Long.valueOf(orgInfo.getId()));
        dept.setDeptName(orgInfo.getOrgChartName());
        dept.setShortForm(orgInfo.getShortForm());
        dept.setLevelId(orgInfo.getLevelId());
        dept.setVersionId(orgInfo.getVersionId());
        dept.setLevelHeadIds(orgInfo.getLevelHeadIds());

        if (StringUtils.isNotBlank(orgInfo.getParentId())) {
            dept.setParentId(Long.valueOf(orgInfo.getParentId()));
        }

        dept.setStatus(SystemConstants.NORMAL);
        dept.setOrderNum(0); // 默认排序

        sysDeptMapper.insert(dept);
        log.debug("插入部门: {}", dept.getDeptName());
    }

    /**
     * 同步用户数据
     *
     * @param orgChartInfoList 组织架构信息列表
     */
    private void syncUsers(List<OrgChartInfo> orgChartInfoList) {
        if (orgChartInfoList == null || orgChartInfoList.isEmpty()) {
            return;
        }

        // 收集所有用户数据
        List<SysUserTreeInfo> allUsers = new ArrayList<>();
        collectAllUsers(orgChartInfoList, allUsers);

        log.info("开始同步用户数据，用户数量: {}", allUsers.size());
        // 获取内嵌密码
        String initPassword = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.user.initPassword");
        String password = BCrypt.hashpw(initPassword);


        for (SysUserTreeInfo userInfo : allUsers) {
            try {
                // 查询用户是否已存在
                SysUser existUser = sysUserMapper.selectOne(
                    new LambdaQueryWrapper<SysUser>().eq(SysUser::getStaffNum, userInfo.getStaffNum())
                );
                if (ObjectUtils.isNotNull(existUser)) {
                    // 用户已存在，执行更新
                    updateUser(existUser, userInfo);
                } else {
                    // 首次同步或用户不存在，执行插入
                    SysUser sysUser = insertUser(userInfo, password);
                    // 插入用户权限(默认普通用户)
                    if (sysUser != null && sysUser.getUserId() != null) {
                        sysUserService.insertUserAuth(sysUser.getUserId(), ArrayUtils.toArray(2L));
                    }
                }
            } catch (Exception e) {
                log.error("同步用户 {} 时发生错误", userInfo, e);
                // 继续处理下一个用户，避免单个用户错误影响整体同步
            }
        }
    }

    /**
     * 递归收集所有用户数据
     *
     * @param orgChartInfoList 组织架构信息列表
     * @param allUsers         收集的所有用户列表
     */
    private void collectAllUsers(List<OrgChartInfo> orgChartInfoList, List<SysUserTreeInfo> allUsers) {
        for (OrgChartInfo orgInfo : orgChartInfoList) {
            if (orgInfo.getSysUserTreeList() != null && !orgInfo.getSysUserTreeList().isEmpty()) {
                for (SysUserTreeInfo userInfo : orgInfo.getSysUserTreeList()) {
                    allUsers.add(userInfo);
                    // 递归处理子用户
                    if (userInfo.getChildren() != null && !userInfo.getChildren().isEmpty()) {
                        collectChildUsers(userInfo.getChildren(), allUsers);
                    }
                }
            }
        }
    }

    /**
     * 递归收集子用户数据
     *
     * @param children 子用户列表
     * @param allUsers 收集的所有用户列表
     */
    private void collectChildUsers(List<SysUserTreeInfo> children, List<SysUserTreeInfo> allUsers) {
        for (SysUserTreeInfo userInfo : children) {
            allUsers.add(userInfo);
            if (userInfo.getChildren() != null && !userInfo.getChildren().isEmpty()) {
                collectChildUsers(userInfo.getChildren(), allUsers);
            }
        }
    }

    /**
     * 更新用户信息
     *
     * @param existUser 已存在的用户对象
     * @param userInfo  用户信息
     */
    private void updateUser(SysUser existUser, SysUserTreeInfo userInfo) {
        try {
            existUser.setStaffName(userInfo.getStaffName());
            existUser.setStaffNameCn(userInfo.getStaffNameCN());
            existUser.setOrgChartName(userInfo.getOrgChartName());
            existUser.setParentId(userInfo.getParentId());
            existUser.setParentNum(userInfo.getParentNum());
            existUser.setJobTitle(userInfo.getJobTitle());

            if (StringUtils.isNotBlank(userInfo.getJoinDate())) {
                DateUtil.parse(userInfo.getJoinDate(), "yyyy-MM-dd HH:mm:ss");
            }

            existUser.setMobilePhone(userInfo.getMobilePhone());
            existUser.setEmail(userInfo.getEmail());
            existUser.setRegionName(userInfo.getRegionName());
            existUser.setAvatar(userInfo.getAvatar());
            existUser.setGradeOriginal(userInfo.getGradeOriginal());
            existUser.setBizLine(userInfo.getBizLine());

            if (StringUtils.isNotBlank(userInfo.getGmt8Modified())) {
                DateUtil.parse(userInfo.getGmt8Modified(), "yyyy-MM-dd HH:mm:ss");
            }

            existUser.setKdCompanyId(userInfo.getKdCompanyId());
            existUser.setKdCompanyName(userInfo.getKdCompanyName());
            existUser.setKdDeptName(userInfo.getKdDeptName());
            existUser.setKdSectionName(userInfo.getKdSectionName());
            existUser.setFinanceDepartment(userInfo.getFinanceDepartment());
            existUser.setIsParent(userInfo.getParent());

            if (StringUtils.isNotBlank(userInfo.getOrgId())) {
                existUser.setDeptId(Long.valueOf(userInfo.getOrgId()));
            }

            existUser.setUpdateTime(new Date());
            sysUserMapper.updateById(existUser);
            log.debug("更新用户: {}", existUser.getStaffName());
        } catch (Exception e) {
            log.error("更新用户 {} 时解析日期发生错误", userInfo.getStaffNum(), e);
        }
    }

    /**
     * 插入新用户
     *
     * @param userInfo 用户信息
     * @param password 密码
     */
    private SysUser insertUser(SysUserTreeInfo userInfo, String password) {
        try {
            SysUser user = new SysUser();
            user.setUserId(Long.valueOf(userInfo.getId()));
            user.setUserName(userInfo.getStaffNum());
            user.setUserType("sys_user");
            user.setStaffNum(userInfo.getStaffNum());
            user.setStaffName(userInfo.getStaffName());
            user.setStaffNameCn(userInfo.getStaffNameCN());

            if (StringUtils.isNotBlank(userInfo.getOrgId())) {
                user.setDeptId(Long.valueOf(userInfo.getOrgId()));
            }

            user.setOrgChartName(userInfo.getOrgChartName());
            user.setParentId(userInfo.getParentId());
            user.setParentNum(userInfo.getParentNum());
            user.setJobTitle(userInfo.getJobTitle());

            if (StringUtils.isNotBlank(userInfo.getJoinDate())) {
                DateUtil.parse(userInfo.getJoinDate(), "yyyy-MM-dd HH:mm:ss");
            }

            user.setMobilePhone(userInfo.getMobilePhone());
            user.setEmail(userInfo.getEmail());
            user.setRegionName(userInfo.getRegionName());
            user.setAvatar(userInfo.getAvatar());
            user.setGradeOriginal(userInfo.getGradeOriginal());
            user.setBizLine(userInfo.getBizLine());

            if (StringUtils.isNotBlank(userInfo.getGmt8Modified())) {
                DateUtil.parse(userInfo.getGmt8Modified(), "yyyy-MM-dd HH:mm:ss");
            }

            user.setKdCompanyId(userInfo.getKdCompanyId());
            user.setKdCompanyName(userInfo.getKdCompanyName());
            user.setKdDeptName(userInfo.getKdDeptName());
            user.setKdSectionName(userInfo.getKdSectionName());
            user.setFinanceDepartment(userInfo.getFinanceDepartment());
            user.setIsParent(userInfo.getParent());

            // 设置默认密码和状态
            user.setPassword(password);
            user.setStatus(SystemConstants.NORMAL);

            sysUserMapper.insert(user);
            log.debug("插入用户: {}", user.getStaffName());
            return user;
        } catch (Exception e) {
            log.error("插入用户 {} 时发生错误", userInfo.getStaffNum(), e);
            return null;
        }
    }

    /**
     * 删除离职用户
     */
    public void deleteLeaveUser() {
        log.info("删除离职用户");
        OaUserPageQry pageQry = new OaUserPageQry();
        pageQry.setPageNum(1);
        pageQry.setPageSize(1000);
        pageQry.setHasLeave("1");
        OrderItem item = new OrderItem();
        item.setAsc(false);
        item.setColumn("id");
        pageQry.setOrderItemList(List.of(item));

        OaUserInfoResult userInfoPage = oaServiceApi.getUserInfoPage(pageQry);

        if (ObjectUtils.isNotNull(userInfoPage) || CollUtil.isNotEmpty(userInfoPage.getRecords())) {
            List<OaUserInfoVo> records = userInfoPage.getRecords();
            List<String> userStaffNums = records.stream().map(OaUserInfoVo::getStaffNum).toList();
            sysUserMapper.delete(new LambdaQueryWrapper<SysUser>().in(SysUser::getStaffNum, userStaffNums));
        }
    }
}
