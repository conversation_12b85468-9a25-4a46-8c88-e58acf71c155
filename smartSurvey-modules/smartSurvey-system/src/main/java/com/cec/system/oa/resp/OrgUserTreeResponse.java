package com.cec.system.oa.resp;

import lombok.Data;

import java.util.List;

/**
 * 组织架构用户树响应实体
 */
@Data
public class OrgUserTreeResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 组织架构用户树数据
     */
    private List<OrgChartInfo> data;

    /**
     * 追踪ID
     */
    private String traceId;
}
