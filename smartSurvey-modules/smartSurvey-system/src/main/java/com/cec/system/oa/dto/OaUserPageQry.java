package com.cec.system.oa.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "用户列表查询条件")
@Data
public class OaUserPageQry {
    @Schema(description = "数据 ID")
    private String id;
    @Schema(description = "职工编号")
    private String staffNum;
    @Schema(description = "职工账号")
    private String staffAccount;
    @Schema(description = "职工姓名")
    private String staffName;
    @Schema(description = "组织机构名称")
    private String orgChartName;
    @Schema(description = "手机号")
    private String mobilePhone;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "是否已离职：0.否；1是")
    private String hasLeave;
    @Schema(description = "当前页码")
    private Integer pageNum = 1;
    @Schema(description = "每页查询的条数 ")
    private Integer pageSize = 20;
    @Schema(description = "数据 ID")
    private List<OrderItem> orderItemList;
}
