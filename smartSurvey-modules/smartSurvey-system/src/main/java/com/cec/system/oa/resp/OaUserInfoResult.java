package com.cec.system.oa.resp;

import com.cec.system.oa.vo.OaUserInfoVo;
import lombok.Data;

import java.util.List;

@Data
public class OaUserInfoResult {
    private List<OaUserInfoVo> records;
    private Integer total;
    private Integer size;
    private Integer current;
    private List<String> orders;
    private Boolean optimizeCountSql;
    private Boolean isSearchCount;
    private Boolean hitCount;
    private String countId;
    private String maxLimit;
    private Boolean searchCount;
    private Integer pages;
}
