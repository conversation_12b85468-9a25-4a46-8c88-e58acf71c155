package com.cec.system.oa.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class GetOaTokenReq implements Serializable {
    private static final long serialVersionUID = 1L;

    private String username;
    private String password;
    private String code;
    private String uuid;
    private String deviceType;
    private String appType;
    /**
     * 平台 ID,默认值为 5.
     * 1：IOS，2：Android，
     * 3：Windows，4：OSX，
     * 5：Web，6：MiniWeb，
     * 7：Linux，8：Android
     * Pad，9：IPad，10：a
     * dmin
     */
    private Integer platformId;
    private String appVersionCode;
}
