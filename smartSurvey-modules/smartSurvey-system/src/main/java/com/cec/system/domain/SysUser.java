package com.cec.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUser extends TenantEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 员工编号
     */
    private String staffNum;

    /**
     * 员工英文名
     */
    private String staffName;

    /**
     * 员工中文名
     */
    private String staffNameCn;

    /**
     * 组织名称
     */
    private String orgChartName;

    /**
     * 上级用户ID
     */
    private String parentId;

    /**
     * 上级员工编号
     */
    private String parentNum;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 入职日期
     */
    private Date joinDate;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地区
     */
    private String regionName;

    /**
     * 用户头像URL
     */
    private String avatar;

    /**
     * 等级
     */
    private String gradeOriginal;

    /**
     * 业务线
     */
    private Integer bizLine;

    /**
     * 修改时间(GMT8)
     */
    private Date gmt8Modified;

    /**
     * 公司ID
     */
    private String kdCompanyId;

    /**
     * 公司名称
     */
    private String kdCompanyName;

    /**
     * 部门名称
     */
    private String kdDeptName;

    /**
     * 科室名称
     */
    private String kdSectionName;

    /**
     * 财务部门
     */
    private String financeDepartment;

    /**
     * 是否是父节点
     */
    private Boolean isParent;

    /**
     * 密码
     */
    @TableField(
        insertStrategy = FieldStrategy.NOT_EMPTY,
        updateStrategy = FieldStrategy.NOT_EMPTY,
        whereStrategy = FieldStrategy.NOT_EMPTY
    )
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;


    public SysUser(Long userId) {
        this.userId = userId;
    }

    public boolean isSuperAdmin() {
        return SystemConstants.SUPER_ADMIN_ID.equals(this.userId);
    }

}
