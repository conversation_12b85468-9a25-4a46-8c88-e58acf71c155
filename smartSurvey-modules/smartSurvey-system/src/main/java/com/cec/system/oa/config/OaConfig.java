package com.cec.system.oa.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "oa")
@Component
@Data
public class OaConfig {
    private String host;
    private String OaTokenAccount;
    private String OaTokenPassword;
    private String publicKey;
    private String appSecret;
    private String pageUserInfoQryURI;
    private String getAccessTokenURI;
    private String getOaLoginTokenURI;
    private String getOrgUserTreeURI;
}
