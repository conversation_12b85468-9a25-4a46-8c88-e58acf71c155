package com.cec.system.oa.resp;

import lombok.Data;

import java.util.List;

/**
 * 组织架构信息实体
 */
@Data
public class OrgChartInfo {

    /**
     * 组织ID
     */
    private String id;

    /**
     * 组织架构名称
     */
    private String orgChartName;

    /**
     * 简称
     */
    private String shortForm;

    /**
     * 级别ID
     */
    private String levelId;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 级别负责人IDs
     */
    private String levelHeadIds;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 系统用户树列表
     */
    private List<SysUserTreeInfo> sysUserTreeList;
}
