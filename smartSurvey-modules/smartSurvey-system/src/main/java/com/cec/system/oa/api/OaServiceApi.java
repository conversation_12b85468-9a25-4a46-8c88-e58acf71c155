package com.cec.system.oa.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.HttpUtils;
import com.cec.system.oa.config.OaConfig;
import com.cec.system.oa.dto.OaUserPageQry;
import com.cec.system.oa.req.GetOaTokenReq;
import com.cec.system.oa.resp.OaUserInfoResult;
import com.cec.system.oa.resp.OrgUserTreeResponse;
import com.cec.system.oa.resp.Response;
import com.cec.system.oa.utils.QueryParamUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Service
public class OaServiceApi {

    @Resource
    private OaConfig oaConfig;

    /**
     * OA 用户登录
     *
     * @param param
     * @return
     */
    public Response oaUserlogin(GetOaTokenReq param, String acceptLanguageHeader) {
        log.info("OA用户登录 param:{}", JSONUtil.toJsonStr(param));
        RSA rsa = new RSA(null, oaConfig.getPublicKey());
        String encryptBase64 = rsa.encryptBase64(param.getPassword(), KeyType.PublicKey);
        param.setPassword(encryptBase64);
        Map<String, String> header = buildHeaderAndSign(param, oaConfig.getGetOaLoginTokenURI());
        header.put("Accept-Language", acceptLanguageHeader);
        String url = oaConfig.getHost() + oaConfig.getGetOaLoginTokenURI();
        log.info("OA用户登录 请求url:{},请求头 header:{}, 参数param: {}", url, JSONUtil.toJsonStr(header), JSONUtil.toJsonStr(param));
        String bodyResp = HttpUtils.postJson(url, param, header);
        log.info("OA用户登录 响应  body {}", bodyResp);
        Response<String> response = JSONUtil.toBean(bodyResp, Response.class);
        return response;
    }

    /**
     * 分页查询接口
     *
     * @param pageQry
     * @return
     */
    public OaUserInfoResult getUserInfoPage(OaUserPageQry pageQry) {
        String queryString = QueryParamUtil.convertPostParamsToGetParams(pageQry);
        Response oaToken = getOAToken();
        if (!oaToken.isSuccess()) {
            throw new ServiceException("获取OA token 失败");
        }
        Map<String, String> param = new HashMap<>();
        param.put("_params_", queryString);
        Map<String, String> header = buildHeaderAndSign(param, oaConfig.getPageUserInfoQryURI());
        header.put("Authorization", oaToken.getData().toString());
        String url = oaConfig.getHost() + oaConfig.getPageUserInfoQryURI() + "?" + queryString;
        log.info("分页查询OA用户信息: 请求参数：{} \n 请求header:{} \n 请求url:{}", queryString, JSONUtil.toJsonStr(header), url);
        HttpRequest request = HttpUtil.createGet(url);
        request.addHeaders(header);
        String result = request.execute().body();
        Response<OaUserInfoResult> response = JSONUtil.toBean(result, new TypeReference<>() {
        }, true);
        if (!response.isSuccess()) {
            log.error("分页查询OA用户信息失败,返回结果:{}", response.getMsg());
            throw new ServiceException(response.getMsg());
        }
        log.info("分页查询OA用户信息,返回结果 total:{}", response.getData().getTotal());
        // 默认头像处理
        OaUserInfoResult infoResult = response.getData();
        if (!CollectionUtils.isEmpty(infoResult.getRecords())) {
//            for (OaUserInfoVo user : infoResult.getRecords()) {
//                String avatar = StrUtil.isEmpty(user.getAvatar()) ? openImConfig.getDefaultFaceUrl() : user.getAvatar();
//                user.setAvatar(avatar);
//            }
        }
        return infoResult;
    }

    /**
     * 获取组织架构用户树
     *
     * @param userDeptIds 部门ID列表
     * @param userIds 用户ID列表
     * @return 组织架构用户树数据
     */
    public OrgUserTreeResponse getOrgUserTree(List<String> userDeptIds, List<String> userIds) {
        log.info("获取组织架构用户树: 开始请求, 部门ID列表: {}, 用户ID列表: {}", userDeptIds, userIds);

        // 获取OA Token
        Response oaToken = getOAToken();
        if (!oaToken.isSuccess()) {
            log.error("获取组织架构用户树: 获取OA token失败");
            throw new ServiceException("获取OA token失败");
        }

        // 构建URL查询参数
        StringBuilder queryParam = new StringBuilder();

        // 添加部门ID参数
        if (userDeptIds != null && !userDeptIds.isEmpty()) {
            for (int i = 0; i < userDeptIds.size(); i++) {
                if (i > 0) {
                    queryParam.append("&");
                }
                queryParam.append("userDeptIds=").append(userDeptIds.get(i));
            }
        }

        // 添加用户ID参数
        if (userIds != null && !userIds.isEmpty()) {
            if (queryParam.length() > 0) {
                queryParam.append("&");
            }
            for (int i = 0; i < userIds.size(); i++) {
                if (i > 0) {
                    queryParam.append("&");
                }
                queryParam.append("userIds=").append(userIds.get(i));
            }
        }

        String queryString = queryParam.toString();
        String url = oaConfig.getHost() + oaConfig.getGetOrgUserTreeURI();

        // 添加查询参数到header进行签名
        Map<String, String> param = new HashMap<>();
        if (!queryString.isEmpty()) {
            param.put("_params_", queryString);
        }

        Map<String, String> header = buildHeaderAndSign(param, oaConfig.getGetOrgUserTreeURI());
        header.put("Authorization", oaToken.getData().toString());

        log.info("获取组织架构用户树: 请求URL: {}, 查询参数: {}", url, queryString);

        // 构建并执行GET请求
        HttpRequest request = HttpUtil.createGet(url);
        if (!queryString.isEmpty()) {
            request.setUrl(url + "?" + queryString);
        }
        request.addHeaders(header);

        String result = request.execute().body();
        log.debug("获取组织架构用户树: 原始响应结果: {}", result);

        OrgUserTreeResponse response = JSONUtil.toBean(result, OrgUserTreeResponse.class);
        if (!response.getSuccess()) {
            log.error("获取组织架构用户树失败, 错误消息: {}", response.getMsg());
            throw new ServiceException(response.getMsg());
        }

        log.info("获取组织架构用户树: 成功获取数据, 数据量: {}",
                response.getData() != null ? response.getData().size() : 0);

        return response;
    }

    /**
     * 获取OAToken
     *
     * @return
     */
    public Response getOAToken() {
        GetOaTokenReq param = new GetOaTokenReq();
        param.setUsername(oaConfig.getOaTokenAccount());
        param.setPlatformId(5);
        RSA rsa = new RSA(null, oaConfig.getPublicKey());
        String encryptBase64 = rsa.encryptBase64(oaConfig.getOaTokenPassword(), KeyType.PublicKey);
        param.setPassword(encryptBase64);
        Map<String, String> header = buildHeaderAndSign(param, oaConfig.getGetAccessTokenURI());
        String url = oaConfig.getHost() + oaConfig.getGetAccessTokenURI();
        log.info("获取OAToken 请求url:{},请求头 header:{}, 参数param: {}", url, JSONUtil.toJsonStr(header), JSONUtil.toJsonStr(param));
        String bodyResp = HttpUtils.postJson(url, param, header);
        log.info("获取OAToken 响应  body {}", bodyResp);
        Response response = JSONUtil.toBean(bodyResp, Response.class);
        return response;
    }

    /**
     * 构建请求头及加签
     *
     * @param param 请求参数
     * @param uri
     * @return
     */
    private Map<String, String> buildHeaderAndSign(Object param, String uri) {
        Map<String, String> header = buildHeader(uri);
        sign(param, header);
        return header;
    }

    private Map<String, String> buildHeader(String uri) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("requestId", IdUtil.fastSimpleUUID());
        header.put("timestamp", System.currentTimeMillis() + "");
        header.put("_url_", uri);
        return header;
    }

    private void sign(Object param, Map<String, String> header) {
        String beforeSignStr = getSignature(param, header);
        log.info("beforeSignStr : {}", beforeSignStr);
        String signature = MD5.create().digestHex(beforeSignStr).toUpperCase();
        header.put("signature", signature);
    }

    /**
     * 获取签名
     *
     * @param param
     * @param header
     * @return
     */
    private String getSignature(Object param, Map<String, String> header) {
        TreeMap<String, Object> sortedParams = new TreeMap<>();
        if (param != null) {
            Map<String, Object> paramMap = BeanUtil.beanToMap(param, false, true);
            sortedParams.putAll(paramMap);
            // 忽略空字符串
            sortedParams.entrySet().removeIf(entry -> entry.getValue() instanceof String && ((String) entry.getValue()).trim().isEmpty());
        }
        sortedParams.put("requestId", header.get("requestId"));
        sortedParams.put("timestamp", header.get("timestamp"));
        sortedParams.put("_url_", header.get("_url_"));
        JSONConfig jsonConfig = JSONConfig.create();
        jsonConfig.setIgnoreNullValue(true);
        String jsonStr = JSONUtil.toJsonStr(sortedParams, jsonConfig);
        return oaConfig.getAppSecret() + jsonStr;
    }
}
