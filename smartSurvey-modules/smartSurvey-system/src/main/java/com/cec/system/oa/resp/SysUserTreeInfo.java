package com.cec.system.oa.resp;

import lombok.Data;

import java.util.List;

/**
 * 系统用户树信息实体
 */
@Data
public class SysUserTreeInfo {

    /**
     * 员工编号
     */
    private String staffNum;

    /**
     * 用户ID
     */
    private String id;

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 组织ID
     */
    private String orgId;

    /**
     * 组织架构名称
     */
    private String orgChartName;

    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 父级编号
     */
    private String parentNum;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 入职日期
     */
    private String joinDate;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 原始等级
     */
    private String gradeOriginal;

    /**
     * 业务线
     */
    private Integer bizLine;

    /**
     * GMT8修改时间
     */
    private String gmt8Modified;

    /**
     * KD公司ID
     */
    private String kdCompanyId;

    /**
     * KD公司名称
     */
    private String kdCompanyName;

    /**
     * KD部门名称
     */
    private String kdDeptName;

    /**
     * KD部门分区名称
     */
    private String kdSectionName;

    /**
     * 中文姓名
     */
    private String staffNameCN;

    /**
     * 财务部门
     */
    private String financeDepartment;

    /**
     * 子节点列表（递归结构）
     */
    private List<SysUserTreeInfo> children;

    /**
     * 是否为父节点
     */
    private Boolean parent;
}
