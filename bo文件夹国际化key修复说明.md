# BO文件夹国际化Key修复说明

## 概述

本次修复针对 `smartSurvey-modules/smartSurvey-business/src/main/java/com/cec/business/domain/bo` 目录下的业务对象类中缺失的国际化key进行了完善，确保所有验证消息都有对应的国际化配置。

## 修复的问题

### 1. 缺失的国际化Key

在BO文件中发现以下国际化key缺失对应的i18n配置：

#### 表单相关
- `form.formKey.not.blank` - 表单key不能为空
- `form.name.not.blank` - 表单名称不能为空

#### 用户表单相关
- `user.form.formKey.not.blank` - 表单key不能为空
- `user.form.name.not.blank` - 表单名称不能为空
- `user.form.item.formItemId.not.blank` - 表单项ID不能为空
- `user.form.item.type.not.blank` - 表单项类型不能为空
- `user.form.item.label.not.blank` - 表单项标题不能为空
- `user.formData.formType.not.blank` - 表单类型不能为空
- `user.formData.userId.not.blank` - 用户ID不能为空
- `user.formData.anonymous.not.blank` - 匿名标识不能为空

#### 活动相关
- `activity.form.name.not.blank` - 活动名称不能为空
- `activity.form.coverImg.not.blank` - 活动封面图不能为空
- `activity.form.formKey.not.blank` - 活动表单key不能为空

#### 验证相关
- `validation.request.error` - 错误请求

### 2. 硬编码中文消息

发现以下文件中存在硬编码的中文验证消息：

1. **UserFormThemeEntity.java**
   - 原始: `@NotBlank(message = "formKey不能为空")`
   - 修复: `@NotBlank(message = "{user.form.formKey.not.blank}")`

2. **UserFormDataDetailEntity.java**
   - 原始: `@NotBlank(message = "错误请求")`
   - 修复: `@NotBlank(message = "{validation.request.error}")`

## 修复的文件

### 国际化资源文件
1. `smartSurvey-admin/src/main/resources/i18n/messages.properties` (简体中文)
2. `smartSurvey-admin/src/main/resources/i18n/messages_zh_CN.properties` (简体中文)
3. `smartSurvey-admin/src/main/resources/i18n/messages_zh_TW.properties` (繁体中文)
4. `smartSurvey-admin/src/main/resources/i18n/messages_en_US.properties` (英文)

### Java类文件
1. `smartSurvey-modules/smartSurvey-business/src/main/java/com/cec/business/domain/bo/UserFormThemeEntity.java`
2. `smartSurvey-modules/smartSurvey-business/src/main/java/com/cec/business/domain/bo/UserFormDataDetailEntity.java`

## 涉及的BO文件分析

### 已正确使用国际化的文件
- `ActivityEntity.java` - 使用了 `{common.id.not.blank}`, `{activity.form.name.not.blank}` 等
- `ActivityApplyEntity.java` - 使用了 `{validation.user.id.not.null}` 等
- `ActivitySigninEntity.java` - 使用了 `{validation.signin.ip.not.blank}` 等
- `FormTemplateEntity.java` - 使用了 `{user.form.formKey.not.blank}` 等
- `UserFormEntity.java` - 使用了 `{form.projectId.not.blank}` 等
- `UserFormItemEntity.java` - 使用了 `{user.form.item.type.not.blank}` 等
- `UserFormDataEntity.java` - 使用了 `{user.formData.formType.not.blank}` 等
- `UserFormLogicEntity.java` - 使用了 `{validation.form.key.not.blank}`

### 无验证注解的文件
- `ProjectInfo.java` - 纯数据对象，无验证注解
- `ProjectRole.java` - 纯数据对象，无验证注解
- `ActivityRole.java` - 纯数据对象，无验证注解
- `ActivityMemberRole.java` - 纯数据对象，无验证注解
- `ProjectMemberRole.java` - 纯数据对象，无验证注解
- `UserFormSettingEntity.java` - 无验证注解
- `UserFormLinkExtEntity.java` - 无验证注解
- `UserFormAuthEntity.java` - 使用了简单的 `@NotBlank` 和 `@NotNull`，无自定义消息

## 验证结果

修复完成后，所有BO文件中的国际化key都有对应的i18n配置：

✅ 所有使用 `{key}` 格式的验证消息都有对应的国际化配置
✅ 硬编码的中文消息已替换为国际化key
✅ 支持简体中文、繁体中文和英文三种语言
✅ 验证消息的语义保持一致

## 注意事项

1. 所有新增的国际化key都遵循现有的命名规范
2. 验证消息的语义在三种语言中保持一致
3. 修复后的代码保持了原有的验证逻辑不变
4. 建议在部署前进行完整的功能测试，确保国际化切换正常工作
