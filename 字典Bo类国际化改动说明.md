# SysDictTypeBo/SysDictDataBo 国际化改动说明

## 概述

本次改动为 `SysDictTypeBo` 和 `SysDictDataBo` 类添加了国际化支持，将硬编码的中文验证消息替换为国际化消息键，支持简体中文、繁体中文和英文三种语言。

## 修改的文件

### 1. Java 类文件
- `smartSurvey-modules/smartSurvey-system/src/main/java/com/cec/system/domain/bo/SysDictTypeBo.java`
- `smartSurvey-modules/smartSurvey-system/src/main/java/com/cec/system/domain/bo/SysDictDataBo.java`

### 2. 国际化资源文件
- `smartSurvey-admin/src/main/resources/i18n/messages.properties` (默认，简体中文)
- `smartSurvey-admin/src/main/resources/i18n/messages_zh_CN.properties` (简体中文)
- `smartSurvey-admin/src/main/resources/i18n/messages_zh_TW.properties` (繁体中文)
- `smartSurvey-admin/src/main/resources/i18n/messages_en_US.properties` (英文)

## 具体改动

### SysDictTypeBo 类改动

**修改前：**
```java
@NotBlank(message = "字典名称不能为空")
@Size(min = 0, max = 100, message = "字典类型名称长度不能超过{max}个字符")
private String dictName;

@NotBlank(message = "字典类型不能为空")
@Size(min = 0, max = 100, message = "字典类型长度不能超过{max}个字符")
@Pattern(regexp = RegexConstants.DICTIONARY_TYPE, message = "字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）")
private String dictType;

@Size(min = 0, max = 500, message = "备注长度不能超过{max}个字符")
private String remark;
```

**修改后：**
```java
@NotBlank(message = "{dict.type.name.not.blank}")
@Size(min = 0, max = 100, message = "{dict.type.name.size}")
private String dictName;

@NotBlank(message = "{dict.type.type.not.blank}")
@Size(min = 0, max = 100, message = "{dict.type.type.size}")
@Pattern(regexp = RegexConstants.DICTIONARY_TYPE, message = "{dict.type.type.pattern}")
private String dictType;

@Size(min = 0, max = 500, message = "{dict.type.remark.size}")
private String remark;
```

### SysDictDataBo 类改动

**修改前：**
```java
@NotBlank(message = "字典标签不能为空")
@Size(min = 0, max = 100, message = "字典标签长度不能超过{max}个字符")
private String dictLabel;

@NotBlank(message = "字典键值不能为空")
@Size(min = 0, max = 100, message = "字典键值长度不能超过{max}个字符")
private String dictValue;

@NotBlank(message = "字典类型不能为空")
@Size(min = 0, max = 100, message = "字典类型长度不能超过{max}个字符")
private String dictType;

@Size(min = 0, max = 100, message = "样式属性长度不能超过{max}个字符")
private String cssClass;

@Size(min = 0, max = 500, message = "备注长度不能超过{max}个字符")
private String remark;
```

**修改后：**
```java
@NotBlank(message = "{dict.data.label.not.blank}")
@Size(min = 0, max = 100, message = "{dict.data.label.size}")
private String dictLabel;

@NotBlank(message = "{dict.data.value.not.blank}")
@Size(min = 0, max = 100, message = "{dict.data.value.size}")
private String dictValue;

@NotBlank(message = "{dict.data.type.not.blank}")
@Size(min = 0, max = 100, message = "{dict.data.type.size}")
private String dictType;

@Size(min = 0, max = 100, message = "{dict.data.css.class.size}")
private String cssClass;

@Size(min = 0, max = 500, message = "{dict.data.remark.size}")
private String remark;
```

## 国际化消息键对照表

### 字典类型 (SysDictTypeBo)

| 消息键 | 简体中文 | 繁体中文 | 英文 |
|--------|----------|----------|------|
| `dict.type.name.not.blank` | 字典名称不能为空 | 字典名稱不能為空 | Dictionary name cannot be blank |
| `dict.type.name.size` | 字典名称长度不能超过{max}个字符 | 字典名稱長度不能超過{max}個字符 | Dictionary name length cannot exceed {max} characters |
| `dict.type.type.not.blank` | 字典类型不能为空 | 字典類型不能為空 | Dictionary type cannot be blank |
| `dict.type.type.size` | 字典类型长度不能超过{max}个字符 | 字典類型長度不能超過{max}個字符 | Dictionary type length cannot exceed {max} characters |
| `dict.type.type.pattern` | 字典类型必须以字母开头，且只能为（小写字母，数字，下划线） | 字典類型必須以字母開頭，且只能為（小寫字母，數字，底線） | Dictionary type must start with a letter and can only contain lowercase letters, numbers, and underscores |
| `dict.type.remark.size` | 备注长度不能超过{max}个字符 | 備註長度不能超過{max}個字符 | Remark length cannot exceed {max} characters |

### 字典数据 (SysDictDataBo)

| 消息键 | 简体中文 | 繁体中文 | 英文 |
|--------|----------|----------|------|
| `dict.data.label.not.blank` | 字典标签不能为空 | 字典標籤不能為空 | Dictionary label cannot be blank |
| `dict.data.label.size` | 字典标签长度不能超过{max}个字符 | 字典標籤長度不能超過{max}個字符 | Dictionary label length cannot exceed {max} characters |
| `dict.data.value.not.blank` | 字典键值不能为空 | 字典鍵值不能為空 | Dictionary value cannot be blank |
| `dict.data.value.size` | 字典键值长度不能超过{max}个字符 | 字典鍵值長度不能超過{max}個字符 | Dictionary value length cannot exceed {max} characters |
| `dict.data.type.not.blank` | 字典类型不能为空 | 字典類型不能為空 | Dictionary type cannot be blank |
| `dict.data.type.size` | 字典类型长度不能超过{max}个字符 | 字典類型長度不能超過{max}個字符 | Dictionary type length cannot exceed {max} characters |
| `dict.data.css.class.size` | 样式属性长度不能超过{max}个字符 | 樣式屬性長度不能超過{max}個字符 | CSS class length cannot exceed {max} characters |
| `dict.data.remark.size` | 备注长度不能超过{max}个字符 | 備註長度不能超過{max}個字符 | Remark length cannot exceed {max} characters |

## 使用方式

### 1. 系统自动根据用户语言环境选择对应的消息
- 默认使用简体中文 (`messages.properties`)
- 用户设置为繁体中文时使用 `messages_zh_TW.properties`
- 用户设置为英文时使用 `messages_en_US.properties`

### 2. 验证消息支持参数替换
- `{max}` 参数会被实际的最大长度值替换
- 例如：`字典名称长度不能超过{max}个字符` → `字典名称长度不能超过100个字符`

## 优势

1. **多语言支持**：支持简体中文、繁体中文、英文三种语言
2. **维护性**：消息集中管理，便于统一修改和维护
3. **扩展性**：可以轻松添加其他语言支持
4. **一致性**：所有字典相关的验证消息使用统一的命名规范
5. **用户体验**：根据用户语言偏好显示对应的错误提示

## 注意事项

1. 确保应用程序已正确配置国际化支持
2. 如需添加新的语言支持，只需在对应的 `.properties` 文件中添加相应的消息键值对
3. 消息键命名遵循 `模块.类型.字段.验证类型` 的规范，便于管理和查找
