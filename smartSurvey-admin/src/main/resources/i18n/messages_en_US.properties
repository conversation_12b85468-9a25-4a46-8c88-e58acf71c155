#错误消息
not.null=* Required fill in
user.jcaptcha.error=Captcha error
user.jcaptcha.expire=Captcha invalid
user.not.exists=Sorry, your account: {0} does not exist
user.password.not.match=User does not exist/Password error
user.password.retry.limit.count=Password input error {0} times
user.password.retry.limit.exceed=Password input error {0} times, account locked for {1} minutes
user.password.delete=Sorry, your account：{0} has been deleted
user.blocked=Sorry, your account: {0} has been disabled. Please contact the administrator
role.blocked=Role disabled，please contact administrators
user.logout.success=Exit successful
length.not.valid=The length must be between {min} and {max} characters
user.username.not.blank=Username cannot be blank
user.username.not.valid=* 2 to 20 chinese characters, letters, numbers or underscores, and must start with a non number
user.username.length.valid=Account length must be between {min} and {max} characters
user.password.not.blank=Password cannot be empty
user.password.length.valid=Password length must be between {min} and {max} characters
user.password.not.valid=* 5-50 characters
user.email.not.valid=Mailbox format error
user.email.not.blank=Mailbox cannot be blank
user.phonenumber.not.blank=Phone number cannot be blank
user.mobile.phone.number.not.valid=Phone number format error
user.login.success=Login successful
user.register.success=Register successful
user.register.save.error=Failed to save user {0}, The registered account already exists
user.register.error=Register failed, please contact system administrator
user.notfound=Please login again
user.forcelogout=The administrator is forced to exit，please login again
user.unknown.error=Unknown error, please login again
auth.grant.type.error=Auth grant type error
auth.grant.type.blocked=Auth grant type disabled
auth.grant.type.not.blank=Auth grant type cannot be blank
auth.clientid.not.blank=Auth clientid cannot be blank
##文件上传消息
upload.exceed.maxSize=The uploaded file size exceeds the limit file size！<br/>the maximum allowed file size is：{0}MB！
upload.filename.exceed.length=The maximum length of uploaded file name is {0} characters
##权限
no.permission=You do not have permission to the data，please contact your administrator to add permissions [{0}]
no.create.permission=You do not have permission to create data，please contact your administrator to add permissions [{0}]
no.update.permission=You do not have permission to modify data，please contact your administrator to add permissions [{0}]
no.delete.permission=You do not have permission to delete data，please contact your administrator to add permissions [{0}]
no.export.permission=You do not have permission to export data，please contact your administrator to add permissions [{0}]
no.view.permission=You do not have permission to view data，please contact your administrator to add permissions [{0}]
repeat.submit.message=Repeat submit is not allowed, please try again later
rate.limiter.message=Visit too frequently, please try again later
sms.code.not.blank=Sms code cannot be blank
sms.code.retry.limit.count=Sms code input error {0} times
sms.code.retry.limit.exceed=Sms code input error {0} times, account locked for {1} minutes
email.code.not.blank=Email code cannot be blank
email.code.retry.limit.count=Email code input error {0} times
email.code.retry.limit.exceed=Email code input error {0} times, account locked for {1} minutes
xcx.code.not.blank=Mini program [code] cannot be blank
social.source.not.blank=Social login platform [source] cannot be blank
social.code.not.blank=Social login platform [code] cannot be blank
social.state.not.blank=Social login platform [state] cannot be blank
##租户
tenant.number.not.blank=Tenant number cannot be blank
tenant.not.exists=Sorry, your tenant does not exist. Please contact the administrator
tenant.blocked=Sorry, your tenant is disabled. Please contact the administrator
tenant.expired=Sorry, your tenant has expired. Please contact the administrator.

common.id.not.blank=Primary key cannot be blank

##Business Error Messages
#Project Management
project.not.exists=Project does not exist
project.user.not.login=User not logged in, unable to query project information
project.user.not.login.access=User not logged in, unable to access project
project.no.permission.modify=No permission to modify project information
project.no.permission.access=You do not have permission to access this project
project.no.permission.delete=No permission to delete project[{0}]

#Form Management
form.not.exists=Survey does not exist
form.status.abnormal.delete=The survey status is abnormal and cannot be deleted
form.status.abnormal.fill=The survey is not published and cannot be filled
form.status.abnormal.publish=The survey status is abnormal and cannot be filled
form.no.valid.items=No valid form items, cannot publish
form.has.answers.cannot.delete=The survey already has responses and cannot be deleted
form.collect.disabled=The survey can no longer be filled
form.not.in.time.range=The survey is not within the fillable time range
form.need.login=This survey requires login to fill
form.exceed.total.count=The survey has exceeded the total fillable count
form.exceed.user.count=The survey has exceeded the user fillable count
form.limited.users.only=This survey is limited to specified users only
form.limited.users.login.required=This survey is limited to specified users, please login first
form.data.empty=Form data cannot be empty
form.fill.failed=Failed to fill form, please try again later
form.item.not.exists=Form item does not exist
form.item.not.text.type=This form item is not a text type, current type: {0}, supported text types: text-input, textarea, number-input
form.item.not.select.type=This form item is not a single or multiple choice type, current type: {0}, supported choice types: select, radio-group, checkbox-group, NSP, rating, matrix-radio
form.item.not.date.type=This form item is not a date type, current type: {0}, supported date types: date-picker
form.item.not.file.type=This form item is not a file type, current type: {0}, supported file types: upload
form.item.no.file.data=This form item has no file data
form.no.valid.file.data=No valid file data found
form.create.temp.file.failed=Failed to create temporary file
form.name.size=Form name length cannot exceed 1000 characters
form.projectId.not.blank=Form projectId cannot be blank
form.formKey.not.blank=Form key cannot be blank
form.name.not.blank=Form name cannot be blank

#User Form Management
user.form.formKey.not.blank=Form key cannot be blank
user.form.name.not.blank=Form name cannot be blank
user.form.item.formItemId.not.blank=Form item ID cannot be blank
user.form.item.type.not.blank=Form item type cannot be blank
user.form.item.label.not.blank=Form item label cannot be blank
user.formData.formType.not.blank=Form type cannot be blank
user.formData.userId.not.blank=User ID cannot be blank
user.formData.anonymous.not.blank=Anonymous flag cannot be blank

#Activity Management
activity.not.exists=Activity does not exist
activity.status.abnormal.delete=Activity status is abnormal, cannot delete
activity.status.abnormal.publish=Activity status is abnormal, cannot publish
activity.status.abnormal.fill=The activity is not published and cannot be filled
activity.no.permission.access=You do not have permission to access this activity
activity.no.permission.delete=No permission to delete this activity
activity.collect.disabled=The activity can no longer be filled
activity.not.in.time.range=The activity is not within the fillable time range
activity.need.apply.first=This activity requires registration before filling, please register first
activity.need.signin.first=This activity requires sign-in before filling, please sign in first
activity.apply.not.exists=Registration activity does not exist
activity.apply.disabled=Registration is not enabled, cannot register
activity.apply.time.invalid=Registration time has passed or not started
activity.apply.limit.exceeded=Registration limit has been reached
activity.signin.not.exists=Sign-in activity does not exist
activity.signin.disabled=Sign-in is not enabled, cannot sign in
activity.signin.time.invalid=Sign-in time has passed or not started
activity.signin.already.exists=Sign-in record already exists
activity.apply.not.approved=Not registered or not approved
activity.user.not.exists=User does not exist
activity.form.name.not.blank=Activity name cannot be blank
activity.form.coverImg.not.blank=Activity cover image cannot be blank
activity.form.formKey.not.blank=Activity form key cannot be blank

#Process Status
process.already.submitted=This document has already been submitted for approval and is under review!
process.already.finished=This document has been completed!
process.already.invalid=This document has been invalidated!
process.already.terminated=This document has been terminated!
process.already.cancelled=This document has been cancelled!
process.already.backed=This document has been returned!
process.status.empty=Process status is empty!

#System Errors
system.operation.failed=Operation failed
system.role.assigned.cannot.disable=Role has been assigned and cannot be disabled!
system.tenant.create.failed=Failed to create tenant
system.invalid.parameter=Invalid parameter
form.no.permission=No form permission

#Validation Messages
validation.user.id.not.null=User ID cannot be null
validation.audit.remark.not.blank=Audit remark cannot be blank
validation.auditor.id.not.null=Auditor ID cannot be null
validation.audit.time.not.null=Audit time cannot be null
validation.signin.ip.not.blank=Sign-in IP cannot be blank
validation.form.data.id.not.null=Form data ID cannot be null
validation.form.key.not.blank=Form key cannot be blank
validation.phone.number.not.blank=Phone number cannot be blank
validation.code.not.blank=Verification code cannot be blank
validation.apply.record.ids.not.null=Application record IDs cannot be null
validation.audit.state.not.null=Audit state cannot be null
validation.audit.remark.length.max=Audit remark length cannot exceed 200 characters
validation.activity.form.key.not.null=Activity form key cannot be null
validation.project.id.not.null=Project ID cannot be null
validation.project.name.not.blank=Project name cannot be blank
validation.project.name.length.max=Project name length cannot exceed 100 characters
validation.project.managers.not.empty=Project managers cannot be empty
validation.data.scope.not.blank=Data scope cannot be blank
validation.menu.check.strictly.not.null=Menu tree selection association display cannot be null
validation.dept.check.strictly.not.null=Department tree selection association display cannot be null
validation.remark.not.blank=Remark cannot be blank
validation.activity.managers.not.empty=Activity managers cannot be empty
validation.form.key.request.error=Form key request error
validation.request.error=Invalid request

#Dictionary Management
dict.type.name.not.blank=Dictionary name cannot be blank
dict.type.name.size=Dictionary name length cannot exceed {max} characters
dict.type.type.not.blank=Dictionary type cannot be blank
dict.type.type.size=Dictionary type length cannot exceed {max} characters
dict.type.type.pattern=Dictionary type must start with a letter and can only contain lowercase letters, numbers, and underscores
dict.type.remark.size=Remark length cannot exceed {max} characters

dict.data.label.not.blank=Dictionary label cannot be blank
dict.data.label.size=Dictionary label length cannot exceed {max} characters
dict.data.value.not.blank=Dictionary value cannot be blank
dict.data.value.size=Dictionary value length cannot exceed {max} characters
dict.data.type.not.blank=Dictionary type cannot be blank
dict.data.type.size=Dictionary type length cannot exceed {max} characters
dict.data.css.class.size=CSS class length cannot exceed {max} characters
dict.data.remark.size=Remark length cannot exceed {max} characters
