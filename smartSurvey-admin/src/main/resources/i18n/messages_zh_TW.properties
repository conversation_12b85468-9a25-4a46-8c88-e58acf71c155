#錯誤訊息
not.null=* 必須填寫
user.jcaptcha.error=驗證碼錯誤
user.jcaptcha.expire=驗證碼已失效
user.not.exists=對不起, 您的帳號：{0} 不存在.
user.password.not.match=使用者不存在/密碼錯誤
user.password.retry.limit.count=密碼輸入錯誤{0}次
user.password.retry.limit.exceed=密碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
user.password.delete=對不起，您的帳號：{0} 已被刪除
user.blocked=對不起，您的帳號：{0} 已禁用，請聯繫管理員
role.blocked=角色已停用，請聯繫管理員
user.logout.success=退出成功
length.not.valid=長度必須在{min}到{max}個字符之間
user.username.not.blank=使用者名稱不能為空
user.username.not.valid=* 2到20個漢字、字母、數字或底線組成，且不能以數字開頭
user.username.length.valid=帳戶長度必須在{min}到{max}個字符之間
user.password.not.blank=使用者密碼不能為空
user.password.length.valid=使用者密碼長度必須在{min}到{max}個字符之間
user.password.not.valid=* 5-50個字符
user.email.not.valid=信箱格式錯誤
user.email.not.blank=信箱不能為空
user.phonenumber.not.blank=使用者手機號不能為空
user.mobile.phone.number.not.valid=手機號格式錯誤
user.login.success=登入成功
user.register.success=註冊成功
user.register.save.error=儲存使用者 {0} 失敗，註冊帳號已存在
user.register.error=註冊失敗，請聯繫系統管理人員
user.notfound=請重新登入
user.forcelogout=管理員強制退出，請重新登入
user.unknown.error=未知錯誤，請重新登入
auth.grant.type.error=認證授權類型錯誤
auth.grant.type.blocked=認證授權類型已禁用
auth.grant.type.not.blank=認證授權類型不能為空
auth.clientid.not.blank=認證客戶端id不能為空
##檔案上傳訊息
upload.exceed.maxSize=上傳的檔案大小超出限制的檔案大小，<br/>允許的檔案最大大小是：{0}MB！
upload.filename.exceed.length=上傳的檔案名最長{0}個字符
##權限
no.permission=您沒有資料的權限，請聯繫管理員添加權限 [{0}]
no.create.permission=您沒有建立資料的權限，請聯繫管理員添加權限 [{0}]
no.update.permission=您沒有修改資料的權限，請聯繫管理員添加權限 [{0}]
no.delete.permission=您沒有刪除資料的權限，請聯繫管理員添加權限 [{0}]
no.export.permission=您沒有匯出資料的權限，請聯繫管理員添加權限 [{0}]
no.view.permission=您沒有檢視資料的權限，請聯繫管理員添加權限 [{0}]
repeat.submit.message=不允許重複提交，請稍後再試
rate.limiter.message=訪問過於頻繁，請稍後再試
sms.code.not.blank=簡訊驗證碼不能為空
sms.code.retry.limit.count=簡訊驗證碼輸入錯誤{0}次
sms.code.retry.limit.exceed=簡訊驗證碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
email.code.not.blank=信箱驗證碼不能為空
email.code.retry.limit.count=信箱驗證碼輸入錯誤{0}次
email.code.retry.limit.exceed=信箱驗證碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
xcx.code.not.blank=小程式[code]不能為空
social.source.not.blank=第三方登入平台[source]不能為空
social.code.not.blank=第三方登入平台[code]不能為空
social.state.not.blank=第三方登入平台[state]不能為空
##租戶
tenant.number.not.blank=租戶編號不能為空
tenant.not.exists=對不起, 您的租戶不存在，請聯繫管理員
tenant.blocked=對不起，您的租戶已禁用，請聯繫管理員
tenant.expired=對不起，您的租戶已過期，請聯繫管理員

common.id.not.blank=主键不能为空

##業務錯誤訊息
#項目管理
project.not.exists=專案不存在
project.user.not.login=使用者未登入，無法查詢專案資訊
project.user.not.login.access=使用者未登入，無法存取專案
project.no.permission.modify=無權限修改專案資訊
project.no.permission.access=您沒有權限存取該專案
project.no.permission.delete=無權限刪除專案[{0}]

#表單管理
form.not.exists=問卷不存在
form.status.abnormal.delete=該問卷狀態異常，無法刪除
form.status.abnormal.fill=該問卷未發布,無法填寫
form.status.abnormal.publish=該問卷狀態異常,無法填寫
form.no.valid.items=無有效表單項，無法發布
form.has.answers.cannot.delete=該問卷已存在答卷，無法刪除
form.collect.disabled=該問卷已無法填寫
form.not.in.time.range=該問卷不在可填寫時間範圍內
form.need.login=該問卷需要登入後才能填寫
form.exceed.total.count=該問卷已超出可填寫總次數
form.exceed.user.count=該問卷已超出使用者可填寫次數
form.limited.users.only=該問卷僅限指定使用者填寫
form.limited.users.login.required=該問卷僅限指定使用者填寫，請先登入
form.data.empty=表單資料不能為空
form.fill.failed=填寫表單失敗，請稍後重試
form.item.not.exists=表單項不存在
form.item.not.text.type=該表單項不是文字類型，目前類型：{0}，支援的文字類型：text-input、textarea、number-input
form.item.not.select.type=該表單項不是單選或多選類型，目前類型：{0}，支援的選擇類型：select、radio-group、checkbox-group、NSP、rating、matrix-radio
form.item.not.date.type=該表單項不是日期類型，目前類型：{0}，支援的日期類型：date-picker
form.item.not.file.type=該表單項不是檔案類型，目前類型：{0}，支援的檔案類型：upload
form.item.no.file.data=該表單項沒有檔案資料
form.no.valid.file.data=沒有找到有效的檔案資料
form.create.temp.file.failed=建立暫存檔案失敗
form.name.size=表單名稱長度不能超過1000個字符
form.projectId.not.blank=項目ID不能為空
form.formKey.not.blank=表單key不能為空
form.name.not.blank=表單名稱不能為空

#用戶表單管理
user.form.formKey.not.blank=表單key不能為空
user.form.name.not.blank=表單名稱不能為空
user.form.item.formItemId.not.blank=表單項ID不能為空
user.form.item.type.not.blank=表單項類型不能為空
user.form.item.label.not.blank=表單項標題不能為空
user.formData.formType.not.blank=表單類型不能為空
user.formData.userId.not.blank=用戶ID不能為空
user.formData.anonymous.not.blank=匿名標識不能為空

#活動管理
activity.not.exists=活動不存在
activity.status.abnormal.delete=活動狀態異常，無法刪除
activity.status.abnormal.publish=活動狀態異常，無法發布
activity.status.abnormal.fill=該活動未發布,無法填寫
activity.no.permission.access=您沒有權限存取該活動
activity.no.permission.delete=沒有權限刪除該活動
activity.collect.disabled=該活動已無法填寫
activity.not.in.time.range=該活動不在可填寫時間範圍內
activity.need.apply.first=該活動為報名後可填寫，請先報名
activity.need.signin.first=該活動為簽到後可填寫，請先簽到
activity.apply.not.exists=報名活動不存在
activity.apply.disabled=報名未開啟，無法報名
activity.apply.time.invalid=報名時間已過或未開始
activity.apply.limit.exceeded=已達到報名人數上限
activity.signin.not.exists=簽到活動不存在
activity.signin.disabled=簽到未開啟，無法簽到
activity.signin.time.invalid=簽到時間已過或未開始
activity.signin.already.exists=已存在簽到記錄
activity.apply.not.approved=未報名或未通過審核
activity.user.not.exists=使用者不存在
activity.form.name.not.blank=活動名稱不能為空
activity.form.coverImg.not.blank=活動封面圖不能為空
activity.form.formKey.not.blank=活動表單key不能為空

#流程狀態
process.already.submitted=該單據已提交過申請,正在審批中！
process.already.finished=該單據已完成申請！
process.already.invalid=該單據已作廢！
process.already.terminated=該單據已終止！
process.already.cancelled=該單據已撤銷！
process.already.backed=該單據已退回！
process.status.empty=流程狀態為空！

#系統錯誤
system.operation.failed=操作失敗
system.role.assigned.cannot.disable=角色已分配，不能禁用!
system.tenant.create.failed=建立租戶失敗
system.invalid.parameter=錯誤的參數
form.no.permission=無表單權限

#驗證訊息
validation.user.id.not.null=用户ID不能為空
validation.audit.remark.not.blank=審核備註不能為空
validation.auditor.id.not.null=審核人ID不能為空
validation.audit.time.not.null=審核時間不能為空
validation.signin.ip.not.blank=簽到IP不能為空
validation.form.data.id.not.null=問卷資料ID不能為空
validation.form.key.not.blank=表單key不能為空
validation.phone.number.not.blank=手機號不能為空
validation.code.not.blank=驗證碼不能為空
validation.apply.record.ids.not.null=報名記錄ID不能為空
validation.audit.state.not.null=審核狀態不能為空
validation.audit.remark.length.max=審核備註長度不能超過200字符
validation.activity.form.key.not.null=活動formKey不能為空
validation.project.id.not.null=主鍵不能為空
validation.project.name.not.blank=專案名稱不能為空
validation.project.name.length.max=專案名稱長度不能超過100個字符
validation.project.managers.not.empty=專案管理員不能為空
validation.data.scope.not.blank=資料範圍不能為空
validation.menu.check.strictly.not.null=選單樹選擇項是否關聯顯示不能為空
validation.dept.check.strictly.not.null=部門樹選擇項是否關聯顯示不能為空
validation.remark.not.blank=備註不能為空
validation.activity.managers.not.empty=活動管理員不能為空
validation.form.key.request.error=key請求異常
validation.request.error=錯誤請求

#字典管理
dict.type.name.not.blank=字典名稱不能為空
dict.type.name.size=字典名稱長度不能超過{max}個字符
dict.type.type.not.blank=字典類型不能為空
dict.type.type.size=字典類型長度不能超過{max}個字符
dict.type.type.pattern=字典類型必須以字母開頭，且只能為（小寫字母，數字，底線）
dict.type.remark.size=備註長度不能超過{max}個字符

dict.data.label.not.blank=字典標籤不能為空
dict.data.label.size=字典標籤長度不能超過{max}個字符
dict.data.value.not.blank=字典鍵值不能為空
dict.data.value.size=字典鍵值長度不能超過{max}個字符
dict.data.type.not.blank=字典類型不能為空
dict.data.type.size=字典類型長度不能超過{max}個字符
dict.data.css.class.size=樣式屬性長度不能超過{max}個字符
dict.data.remark.size=備註長度不能超過{max}個字符
