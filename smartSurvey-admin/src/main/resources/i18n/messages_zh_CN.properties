#错误消息
not.null=* 必须填写
user.jcaptcha.error=验证码错误
user.jcaptcha.expire=验证码已失效
user.not.exists=对不起, 您的账号：{0} 不存在.
user.password.not.match=用户不存在/密码错误
user.password.retry.limit.count=密码输入错误{0}次
user.password.retry.limit.exceed=密码输入错误{0}次，帐户锁定{1}分钟
user.password.delete=对不起，您的账号：{0} 已被删除
user.blocked=对不起，您的账号：{0} 已禁用，请联系管理员
role.blocked=角色已封禁，请联系管理员
user.logout.success=退出成功
length.not.valid=长度必须在{min}到{max}个字符之间
user.username.not.blank=用户名不能为空
user.username.not.valid=* 2到20个汉字、字母、数字或下划线组成，且必须以非数字开头
user.username.length.valid=账户长度必须在{min}到{max}个字符之间
user.password.not.blank=用户密码不能为空
user.password.length.valid=用户密码长度必须在{min}到{max}个字符之间
user.password.not.valid=* 5-50个字符
user.email.not.valid=邮箱格式错误
user.email.not.blank=邮箱不能为空
user.phonenumber.not.blank=用户手机号不能为空
user.mobile.phone.number.not.valid=手机号格式错误
user.login.success=登录成功
user.register.success=注册成功
user.register.save.error=保存用户 {0} 失败，注册账号已存在
user.register.error=注册失败，请联系系统管理人员
user.notfound=请重新登录
user.forcelogout=管理员强制退出，请重新登录
user.unknown.error=未知错误，请重新登录
auth.grant.type.error=认证权限类型错误
auth.grant.type.blocked=认证权限类型已禁用
auth.grant.type.not.blank=认证权限类型不能为空
auth.clientid.not.blank=认证客户端id不能为空
##文件上传消息
upload.exceed.maxSize=上传的文件大小超出限制的文件大小！<br/>允许的文件最大大小是：{0}MB！
upload.filename.exceed.length=上传的文件名最长{0}个字符
##权限
no.permission=您没有数据的权限，请联系管理员添加权限 [{0}]
no.create.permission=您没有创建数据的权限，请联系管理员添加权限 [{0}]
no.update.permission=您没有修改数据的权限，请联系管理员添加权限 [{0}]
no.delete.permission=您没有删除数据的权限，请联系管理员添加权限 [{0}]
no.export.permission=您没有导出数据的权限，请联系管理员添加权限 [{0}]
no.view.permission=您没有查看数据的权限，请联系管理员添加权限 [{0}]
repeat.submit.message=不允许重复提交，请稍候再试
rate.limiter.message=访问过于频繁，请稍候再试
sms.code.not.blank=短信验证码不能为空
sms.code.retry.limit.count=短信验证码输入错误{0}次
sms.code.retry.limit.exceed=短信验证码输入错误{0}次，帐户锁定{1}分钟
email.code.not.blank=邮箱验证码不能为空
email.code.retry.limit.count=邮箱验证码输入错误{0}次
email.code.retry.limit.exceed=邮箱验证码输入错误{0}次，帐户锁定{1}分钟
xcx.code.not.blank=小程序[code]不能为空
social.source.not.blank=第三方登录平台[source]不能为空
social.code.not.blank=第三方登录平台[code]不能为空
social.state.not.blank=第三方登录平台[state]不能为空
##租户
tenant.number.not.blank=租户编号不能为空
tenant.not.exists=对不起, 您的租户不存在，请联系管理员
tenant.blocked=对不起，您的租户已禁用，请联系管理员
tenant.expired=对不起，您的租户已过期，请联系管理员

common.id.not.blank=主键不能为空

##业务错误消息
#项目管理
project.not.exists=项目不存在
project.user.not.login=用户未登录，无法查询项目信息
project.user.not.login.access=用户未登录，无法访问项目
project.no.permission.modify=无权限修改项目信息
project.no.permission.access=您没有权限访问该项目
project.no.permission.delete=无权限删除项目[{0}]

#表单管理
form.not.exists=问卷不存在
form.status.abnormal.delete=该问卷状态异常，无法删除
form.status.abnormal.fill=该问卷未发布,无法填写
form.status.abnormal.publish=该问卷状态异常,无法填写
form.no.valid.items=无有效表单项，无法发布
form.has.answers.cannot.delete=该问卷已存在答卷，无法删除
form.collect.disabled=该问卷已无法填写
form.not.in.time.range=该问卷不在可填写时间范围内
form.need.login=该问卷需要登录后才能填写
form.exceed.total.count=该问卷已超出可填写总次数
form.exceed.user.count=该问卷已超出用户可填写次数
form.limited.users.only=该问卷仅限指定用户填写
form.limited.users.login.required=该问卷仅限指定用户填写，请先登录
form.data.empty=表单数据不能为空
form.fill.failed=填写表单失败，请稍后重试
form.item.not.exists=表单项不存在
form.item.not.text.type=该表单项不是文本类型，当前类型：{0}，支持的文本类型：text-input、textarea、number-input
form.item.not.select.type=该表单项不是单选或多选类型，当前类型：{0}，支持的选择类型：select、radio-group、checkbox-group、NSP、rating、matrix-radio
form.item.not.date.type=该表单项不是日期类型，当前类型：{0}，支持的日期类型：date-picker
form.item.not.file.type=该表单项不是文件类型，当前类型：{0}，支持的文件类型：upload
form.item.no.file.data=该表单项没有文件数据
form.no.valid.file.data=没有找到有效的文件数据
form.create.temp.file.failed=创建临时文件失败
form.name.size=表单名称长度不能超过1000个字符
form.projectId.not.blank=项目ID不能为空
form.formKey.not.blank=表单key不能为空
form.name.not.blank=表单名称不能为空

#用户表单管理
user.form.formKey.not.blank=表单key不能为空
user.form.name.not.blank=表单名称不能为空
user.form.item.formItemId.not.blank=表单项ID不能为空
user.form.item.type.not.blank=表单项类型不能为空
user.form.item.label.not.blank=表单项标题不能为空
user.formData.formType.not.blank=表单类型不能为空
user.formData.userId.not.blank=用户ID不能为空
user.formData.anonymous.not.blank=匿名标识不能为空

#活动管理
activity.not.exists=活动不存在
activity.status.abnormal.delete=活动状态异常，无法删除
activity.status.abnormal.publish=活动状态异常，无法发布
activity.status.abnormal.fill=该活动未发布,无法填写
activity.no.permission.access=您没有权限访问该活动
activity.no.permission.delete=没有权限删除该活动
activity.collect.disabled=该活动已无法填写
activity.not.in.time.range=该活动不在可填写时间范围内
activity.need.apply.first=该活动为报名后可填写，请先报名
activity.need.signin.first=该活动为签到后可填写，请先签到
activity.apply.not.exists=报名活动不存在
activity.apply.disabled=报名未开启，无法报名
activity.apply.time.invalid=报名时间已过或未开始
activity.apply.limit.exceeded=已达到报名人数上限
activity.signin.not.exists=签到活动不存在
activity.signin.disabled=签到未开启，无法签到
activity.signin.time.invalid=签到时间已过或未开始
activity.signin.already.exists=已存在签到记录
activity.apply.not.approved=未报名或未通过审核
activity.user.not.exists=用户不存在
activity.form.name.not.blank=活动名称不能为空
activity.form.coverImg.not.blank=活动封面图不能为空
activity.form.formKey.not.blank=活动表单key不能为空

#流程状态
process.already.submitted=该单据已提交过申请,正在审批中！
process.already.finished=该单据已完成申请！
process.already.invalid=该单据已作废！
process.already.terminated=该单据已终止！
process.already.cancelled=该单据已撤销！
process.already.backed=该单据已退回！
process.status.empty=流程状态为空！

#系统错误
system.operation.failed=操作失败
system.role.assigned.cannot.disable=角色已分配，不能禁用!
system.tenant.create.failed=创建租户失败
system.invalid.parameter=错误的参数
form.no.permission=无表单权限

#验证消息
validation.user.id.not.null=用户ID不能为空
validation.audit.remark.not.blank=审核备注不能为空
validation.auditor.id.not.null=审核人ID不能为空
validation.audit.time.not.null=审核时间不能为空
validation.signin.ip.not.blank=签到IP不能为空
validation.form.data.id.not.null=问卷数据ID不能为空
validation.form.key.not.blank=表单key不能为空
validation.phone.number.not.blank=手机号不能为空
validation.code.not.blank=验证码不能为空
validation.apply.record.ids.not.null=报名记录ID不能为空
validation.audit.state.not.null=审核状态不能为空
validation.audit.remark.length.max=审核备注长度不能超过200
validation.activity.form.key.not.null=活动formKey不能为空
validation.project.id.not.null=主键不能为空
validation.project.name.not.blank=项目名称不能为空
validation.project.name.length.max=项目名称长度不能超过100个字符
validation.project.managers.not.empty=项目管理员不能为空
validation.data.scope.not.blank=数据范围不能为空
validation.menu.check.strictly.not.null=菜单树选择项是否关联显示不能为空
validation.dept.check.strictly.not.null=部门树选择项是否关联显示不能为空
validation.remark.not.blank=备注不能为空
validation.activity.managers.not.empty=活动管理员不能为空
validation.form.key.request.error=key请求异常
validation.request.error=错误请求

#字典管理
dict.type.name.not.blank=字典名称不能为空
dict.type.name.size=字典名称长度不能超过{max}个字符
dict.type.type.not.blank=字典类型不能为空
dict.type.type.size=字典类型长度不能超过{max}个字符
dict.type.type.pattern=字典类型必须以字母开头，且只能为（小写字母，数字，下划线）
dict.type.remark.size=备注长度不能超过{max}个字符

dict.data.label.not.blank=字典标签不能为空
dict.data.label.size=字典标签长度不能超过{max}个字符
dict.data.value.not.blank=字典键值不能为空
dict.data.value.size=字典键值长度不能超过{max}个字符
dict.data.type.not.blank=字典类型不能为空
dict.data.type.size=字典类型长度不能超过{max}个字符
dict.data.css.class.size=样式属性长度不能超过{max}个字符
dict.data.remark.size=备注长度不能超过{max}个字符
