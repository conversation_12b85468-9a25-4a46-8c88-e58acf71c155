--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: *******************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: Wen@3gj7*h2a
#        # 从库数据源
#        slave:
#          lazy: true
#          type: ${spring.datasource.type}
#          driverClassName: com.mysql.cj.jdbc.Driver
#          url: **********************************************************************************************************************************************************************************************************************************************************
#          username:
#          password:
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: *************************************
#          username: ROOT
#          password: root
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ******************************************************************************************************************************************
#          username: root
#          password: root
#        sqlserver:
#          type: ${spring.datasource.type}
#          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: *******************************************************************************************************************
#          username: SA
#          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: ***********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 3
    # redis 密码必须配置
    password: zhengjiamax.com
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称 不能用中文
    clientName: smartSurvey
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: xxxxxxxxxx
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # OSS配置
oss:
  # 是否启用配置文件配置
  enabled: true
  # 默认配置Key
  default-config: minio
  # OSS配置项集合
  configs:
    # minio配置
    minio:
      # 访问站点
      endpoint: ***********:10005
      # 自定义域名（为空则使用endpoint）
      domain:
      # 前缀
      prefix:
      # 访问密钥
      accessKey: GSlbypxqLU1Kk0cPTwWQ
      # 密钥
      secretKey: 2l7PmS5OHU6VWM3gj7JLrGvm9t2rf1O5fZHYnduZ
      # 存储空间名
      bucketName: smart-survey
      # 存储区域
      region:
      # 是否使用https（Y=是,N=否）
      isHttps: N
      # 桶权限类型(0=private私有, 1=public公共读写, 2=custom自定义-公共读私有写)
      accessPolicy: 1

h5:
  host: http://************:8092/

oa:
  host: http://***********:8686
  appSecret: g2rcN8j0di!s
  OaTokenAccount: sysAdmin
  OaTokenPassword: LX)oN20KX~2!
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD0BCSaKhAeA8kQS4pK7QGaFwZ4MJCdU9fUdbYVALts6U+TEvfWXsyRcLQfmHq3bSl3QE2CGbgt/tKznKWS9ODyUwpfz7/+zAuDVlPD4opHy+ni9zbxefsEN4VtyFoTBiO7BAAxWjPXhHir6hZUcF5ZTJsW43wTdcdajuqxn67mUwIDAQAB
  getOrgUserTreeURI: /openapi/im/sysUser/getOrgUserTree
  pageUserInfoQryURI: /openapi/im/sysUser/page/list
  getAccessTokenURI: /openapi/im/sys/getAccessToken
  getOaLoginTokenURI: /openapi/im/sys/loginOpenIM
