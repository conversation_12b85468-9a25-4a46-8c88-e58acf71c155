package com.cec.web.task;

import com.cec.common.core.domain.R;
import com.cec.common.mybatis.helper.DataPermissionHelper;
import com.cec.system.service.SyncUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@AllArgsConstructor
public class ScheduledTasks {

    private final RedissonClient redissonClient;
    private final SyncUserService syncUserService;

    /**
     * 定时同步OA用户数据，每天凌晨2点执行一次
     * 此定时任务仅执行增量同步，系统首次启动时的全量同步由SyncUserService的初始化方法负责
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncOaUserTask() {
        String lockKey = "scheduled:task:syncOaUserTask";
        RLock lock = redissonClient.getLock(lockKey);
        long startTime = System.currentTimeMillis();

        try {
            log.debug("尝试获取OA用户同步定时任务锁: {}", lockKey);
            // 最多等待3秒获取锁，获取到后锁定30分钟（预计足够完成同步任务）
            boolean isLocked = lock.tryLock(3, TimeUnit.SECONDS);
            if (isLocked) {
                try {
                    log.info("成功获取到锁 [{}]，开始执行OA用户增量同步任务", lockKey);

                    // 使用忽略数据权限的方式执行同步操作
                    // 这样可以避免需要 SaToken 上下文的问题
                    R<String> result = DataPermissionHelper.ignore(syncUserService::syncUser);

                    long executionTime = System.currentTimeMillis() - startTime;
                    if (result.getCode() == 200) {
                        log.info("OA用户增量同步任务执行成功，耗时: {}ms", executionTime);
                    } else {
                        log.warn("OA用户增量同步任务执行异常，耗时: {}ms，错误信息: {}", executionTime, result.getMsg());
                    }
                } catch (Exception e) {
                    log.error("OA用户增量同步任务执行异常", e);
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("已释放OA用户同步定时任务锁: {}", lockKey);
                    }
                }
            } else {
                log.warn("无法获取到OA用户同步定时任务锁，可能有其他实例正在执行同步任务");
            }
        } catch (InterruptedException e) {
            log.error("获取OA用户同步分布式锁异常: {}", lockKey, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("OA用户同步定时任务锁处理过程中发生未预期异常", e);
        }
    }
}
