package com.cec;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@EnableScheduling
@SpringBootApplication
public class SmartSurveyApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(SmartSurveyApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  Smart-Survey启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
