package com.cec.stat;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NSP和Rating类型测试
 */
public class NSPAndRatingTest {

    /**
     * 模拟解析答题值的方法
     */
    private List<String> parseAnswerValue(String answerValue, String itemType) {
        if (answerValue == null || answerValue.trim().isEmpty()) {
            return Arrays.asList();
        }
        
        // 单选类型：直接返回单个选项（包括NSP和Rating）
        if ("select".equals(itemType) || "radio-group".equals(itemType) || 
            "nsp".equals(itemType) || "rating".equals(itemType) || "rate".equals(itemType)) {
            return Arrays.asList(answerValue.trim());
        }
        
        // 多选类型：解析数组格式 [选项1, 选项2]
        if ("checkbox-group".equals(itemType)) {
            // 移除首尾的方括号
            String cleanValue = answerValue.trim();
            if (cleanValue.startsWith("[") && cleanValue.endsWith("]")) {
                cleanValue = cleanValue.substring(1, cleanValue.length() - 1);
            }
            
            // 按逗号分割并清理空格
            return Arrays.stream(cleanValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
        }
        
        // 默认情况：当作单选处理
        return Arrays.asList(answerValue.trim());
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return String.format("%.1f%%", ratio * 100);
    }

    @Test
    public void testNSPLogic() {
        System.out.println("=== NSP（净推荐值）测试 ===");
        
        // 模拟NSP答题数据：5个人填写了NSP题目（通常是0-10分）
        String[] nspAnswers = {
            "9",  // 第1个人给了9分（推荐者）
            "7",  // 第2个人给了7分（中性者）
            "10", // 第3个人给了10分（推荐者）
            "6",  // 第4个人给了6分（批评者）
            "8"   // 第5个人给了8分（中性者）
        };
        
        // 统计每个分数被选择的人数
        Map<String, Long> nspCountMap = new HashMap<>();
        
        for (String answer : nspAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "nsp");
            System.out.println("NSP答题: " + answer + " -> 解析结果: " + selectedOptions);
            
            for (String option : selectedOptions) {
                nspCountMap.merge(option, 1L, Long::sum);
            }
        }
        
        long fillCount = nspAnswers.length; // 填写该题的总人数 = 5
        
        System.out.println("\n=== NSP统计结果 ===");
        System.out.println("填写该题的总人数: " + fillCount);
        
        nspCountMap.entrySet().stream()
            .sorted((a, b) -> Integer.compare(Integer.parseInt(b.getKey()), Integer.parseInt(a.getKey())))
            .forEach(entry -> {
                String score = entry.getKey();
                Long count = entry.getValue();
                String fillRate = formatPercentage(count, fillCount);
                System.out.println("分数" + score + ": " + count + "人选择 (" + fillRate + ")");
            });
        
        // 验证结果
        assert nspCountMap.get("9").equals(1L);
        assert nspCountMap.get("7").equals(1L);
        assert nspCountMap.get("10").equals(1L);
        assert nspCountMap.get("6").equals(1L);
        assert nspCountMap.get("8").equals(1L);
        
        System.out.println("✓ NSP统计正确！");
    }

    @Test
    public void testRatingLogic() {
        System.out.println("\n=== Rating（评分）测试 ===");
        
        // 模拟Rating答题数据：4个人填写了评分题目（通常是1-5星）
        String[] ratingAnswers = {
            "5",  // 第1个人给了5星
            "4",  // 第2个人给了4星
            "5",  // 第3个人给了5星
            "3"   // 第4个人给了3星
        };
        
        // 统计每个评分被选择的人数
        Map<String, Long> ratingCountMap = new HashMap<>();
        
        for (String answer : ratingAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "rating");
            System.out.println("Rating答题: " + answer + " -> 解析结果: " + selectedOptions);
            
            for (String option : selectedOptions) {
                ratingCountMap.merge(option, 1L, Long::sum);
            }
        }
        
        long fillCount = ratingAnswers.length; // 填写该题的总人数 = 4
        
        System.out.println("\n=== Rating统计结果 ===");
        System.out.println("填写该题的总人数: " + fillCount);
        
        ratingCountMap.entrySet().stream()
            .sorted((a, b) -> Integer.compare(Integer.parseInt(b.getKey()), Integer.parseInt(a.getKey())))
            .forEach(entry -> {
                String star = entry.getKey();
                Long count = entry.getValue();
                String fillRate = formatPercentage(count, fillCount);
                System.out.println(star + "星: " + count + "人选择 (" + fillRate + ")");
            });
        
        // 验证结果
        assert ratingCountMap.get("5").equals(2L); // 2人给了5星，50%
        assert ratingCountMap.get("4").equals(1L); // 1人给了4星，25%
        assert ratingCountMap.get("3").equals(1L); // 1人给了3星，25%
        
        System.out.println("✓ Rating统计正确！");
    }

    @Test
    public void testRateLogic() {
        System.out.println("\n=== Rate（评分）测试 ===");
        
        // 模拟Rate答题数据：3个人填写了评分题目
        String[] rateAnswers = {
            "优秀",  // 第1个人选择了优秀
            "良好",  // 第2个人选择了良好
            "优秀"   // 第3个人选择了优秀
        };
        
        // 统计每个评分被选择的人数
        Map<String, Long> rateCountMap = new HashMap<>();
        
        for (String answer : rateAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "rate");
            System.out.println("Rate答题: " + answer + " -> 解析结果: " + selectedOptions);
            
            for (String option : selectedOptions) {
                rateCountMap.merge(option, 1L, Long::sum);
            }
        }
        
        long fillCount = rateAnswers.length; // 填写该题的总人数 = 3
        
        System.out.println("\n=== Rate统计结果 ===");
        System.out.println("填写该题的总人数: " + fillCount);
        
        rateCountMap.forEach((rating, count) -> {
            String fillRate = formatPercentage(count, fillCount);
            System.out.println(rating + ": " + count + "人选择 (" + fillRate + ")");
        });
        
        // 验证结果
        assert rateCountMap.get("优秀").equals(2L); // 2人选择优秀，66.7%
        assert rateCountMap.get("良好").equals(1L); // 1人选择良好，33.3%
        
        System.out.println("✓ Rate统计正确！");
    }

    @Test
    public void testMixedTypes() {
        System.out.println("\n=== 混合类型处理测试 ===");
        
        // 测试不同类型的处理是否一致
        String[] testCases = {
            "select:选项A",
            "radio-group:选项B", 
            "nsp:8",
            "rating:4",
            "rate:良好"
        };
        
        for (String testCase : testCases) {
            String[] parts = testCase.split(":");
            String type = parts[0];
            String value = parts[1];
            
            List<String> result = parseAnswerValue(value, type);
            System.out.println("类型: " + type + ", 值: " + value + " -> 解析结果: " + result);
            
            // 所有单选类型都应该返回单个元素的列表
            assert result.size() == 1;
            assert result.get(0).equals(value);
        }
        
        System.out.println("✓ 混合类型处理正确！");
    }
}
