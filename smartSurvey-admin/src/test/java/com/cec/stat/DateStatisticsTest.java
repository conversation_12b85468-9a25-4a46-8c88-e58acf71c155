package com.cec.stat;

import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 日期统计测试
 */
public class DateStatisticsTest {

    // 模拟常量
    private static final String ITEM_TYPE_DATE_PICKER = "date-picker";

    /**
     * 判断是否为日期类型
     */
    private static boolean isDateType(String itemType) {
        return ITEM_TYPE_DATE_PICKER.equals(itemType);
    }

    /**
     * 根据统计类型格式化日期
     */
    private String formatDateByStatType(LocalDate date, String statType) {
        return switch (statType) {
            case "year" -> String.valueOf(date.getYear());
            case "month" -> date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case "day" -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            default -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        };
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return String.format("%.1f%%", ratio * 100);
    }

    @Test
    public void testDateTypeValidation() {
        System.out.println("=== 日期类型验证测试 ===");

        // 测试日期类型识别
        assert isDateType("date-picker");
        assert !isDateType("text-input");
        assert !isDateType("select");

        System.out.println("✓ 日期类型验证正确");
    }

    @Test
    public void testDateFormatting() {
        System.out.println("\n=== 日期格式化测试 ===");

        LocalDate testDate = LocalDate.of(2025, 4, 24);

        // 测试不同统计类型的日期格式化
        String yearFormat = formatDateByStatType(testDate, "year");
        String monthFormat = formatDateByStatType(testDate, "month");
        String dayFormat = formatDateByStatType(testDate, "day");

        System.out.println("原始日期: " + testDate);
        System.out.println("年度格式: " + yearFormat);
        System.out.println("月度格式: " + monthFormat);
        System.out.println("日期格式: " + dayFormat);

        // 验证格式化结果
        assert "2025".equals(yearFormat);
        assert "2025-04".equals(monthFormat);
        assert "2025-04-24".equals(dayFormat);

        System.out.println("✓ 日期格式化正确");
    }

    @Test
    public void testTimestampStatistics() {
        System.out.println("\n=== 时间戳统计逻辑测试 ===");

        // 模拟时间戳答题数据（毫秒）
        // 1753200000000 对应 2025-07-27
        // 1753286400000 对应 2025-07-28
        // 1756051200000 对应 2025-08-28
        String[] timestampAnswers = {
            "1753200000000",  // 2025-07-27
            "1753200000000",  // 2025-07-27
            "1753286400000",  // 2025-07-28
            "1756051200000",  // 2025-08-28
            "1756051200000",  // 2025-08-28
            "1756051200000"   // 2025-08-28
        };

        // 测试按日统计
        testTimestampStatisticsByType(timestampAnswers, "day", "按日统计");

        // 测试按月统计
        testTimestampStatisticsByType(timestampAnswers, "month", "按月统计");

        // 测试按年统计
        testTimestampStatisticsByType(timestampAnswers, "year", "按年统计");

        System.out.println("✓ 时间戳统计逻辑正确");
    }

    @Test
    public void testDateStatistics() {
        System.out.println("\n=== 日期统计逻辑测试（兼容性测试）===");

        // 模拟日期答题数据
        String[] dateAnswers = {
            "2025-04-24",
            "2025-04-24",
            "2025-04-25",
            "2025-05-01",
            "2025-05-01",
            "2025-05-01"
        };

        // 测试按日统计
        testStatisticsByType(dateAnswers, "day", "按日统计");

        // 测试按月统计
        testStatisticsByType(dateAnswers, "month", "按月统计");

        // 测试按年统计
        testStatisticsByType(dateAnswers, "year", "按年统计");

        System.out.println("✓ 日期统计逻辑正确");
    }

    private void testTimestampStatisticsByType(String[] timestampAnswers, String statType, String description) {
        System.out.println("\n--- " + description + " (时间戳) ---");

        Map<String, Long> dateCountMap = new HashMap<>();

        // 统计每个时间戳的填写次数
        for (String answerValue : timestampAnswers) {
            try {
                // 解析时间戳（毫秒）
                long timestamp = Long.parseLong(answerValue);
                // 将时间戳转换为LocalDate（使用系统默认时区）
                LocalDate date = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
                String dateKey = formatDateByStatType(date, statType);
                dateCountMap.merge(dateKey, 1L, Long::sum);

                // 输出转换结果用于调试
                System.out.println("时间戳 " + answerValue + " -> " + date + " -> " + dateKey);
            } catch (Exception e) {
                System.out.println("无法解析时间戳值: " + answerValue);
            }
        }

        long totalFillCount = timestampAnswers.length;

        // 输出统计结果
        dateCountMap.entrySet().stream()
            .sorted((a, b) -> a.getKey().compareTo(b.getKey()))
            .forEach(entry -> {
                String dateKey = entry.getKey();
                Long count = entry.getValue();
                String fillRate = formatPercentage(count, totalFillCount);
                System.out.println(dateKey + ": " + count + "次 (" + fillRate + ")");
            });
    }

    private void testStatisticsByType(String[] dateAnswers, String statType, String description) {
        System.out.println("\n--- " + description + " ---");

        Map<String, Long> dateCountMap = new HashMap<>();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 统计每个日期的填写次数
        for (String answerValue : dateAnswers) {
            try {
                LocalDate date = LocalDate.parse(answerValue, inputFormatter);
                String dateKey = formatDateByStatType(date, statType);
                dateCountMap.merge(dateKey, 1L, Long::sum);
            } catch (Exception e) {
                System.out.println("无法解析日期值: " + answerValue);
            }
        }

        long totalFillCount = dateAnswers.length;

        // 输出统计结果
        dateCountMap.entrySet().stream()
            .sorted((a, b) -> a.getKey().compareTo(b.getKey()))
            .forEach(entry -> {
                String dateKey = entry.getKey();
                Long count = entry.getValue();
                String fillRate = formatPercentage(count, totalFillCount);
                System.out.println(dateKey + ": " + count + "次 (" + fillRate + ")");
            });

        // 验证统计结果
        switch (statType) {
            case "day":
                assert dateCountMap.get("2025-04-24").equals(2L);
                assert dateCountMap.get("2025-04-25").equals(1L);
                assert dateCountMap.get("2025-05-01").equals(3L);
                break;
            case "month":
                assert dateCountMap.get("2025-04").equals(3L);
                assert dateCountMap.get("2025-05").equals(3L);
                break;
            case "year":
                assert dateCountMap.get("2025").equals(6L);
                break;
        }
    }

    @Test
    public void testTimestampConversion() {
        System.out.println("\n=== 时间戳转换测试 ===");

        // 测试具体的时间戳转换
        String[] testTimestamps = {
            "1753200000000",  // 应该对应某个具体日期
            "1640995200000",  // 2022-01-01 00:00:00 UTC
            "1672531200000"   // 2023-01-01 00:00:00 UTC
        };

        for (String timestampStr : testTimestamps) {
            try {
                long timestamp = Long.parseLong(timestampStr);
                LocalDate date = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();

                System.out.println("时间戳: " + timestampStr + " -> 日期: " + date);
                System.out.println("  年度格式: " + formatDateByStatType(date, "year"));
                System.out.println("  月度格式: " + formatDateByStatType(date, "month"));
                System.out.println("  日期格式: " + formatDateByStatType(date, "day"));
                System.out.println();

            } catch (Exception e) {
                System.out.println("转换失败: " + timestampStr + " -> " + e.getMessage());
            }
        }

        System.out.println("✓ 时间戳转换测试完成");
    }

    @Test
    public void testInvalidDates() {
        System.out.println("\n=== 无效日期处理测试 ===");

        String[] invalidDateAnswers = {
            "2025-04-24",  // 有效日期
            "invalid-date", // 无效日期
            "",            // 空值
            "2025-13-01",  // 无效月份
            "2025-04-32"   // 无效日期
        };

        Map<String, Long> dateCountMap = new HashMap<>();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        int validCount = 0;

        for (String answerValue : invalidDateAnswers) {
            if (answerValue == null || answerValue.trim().isEmpty()) {
                System.out.println("跳过空值: \"" + answerValue + "\"");
                continue;
            }

            try {
                LocalDate date = LocalDate.parse(answerValue, inputFormatter);
                String dateKey = formatDateByStatType(date, "day");
                dateCountMap.merge(dateKey, 1L, Long::sum);
                validCount++;
                System.out.println("有效日期: " + answerValue + " -> " + dateKey);
            } catch (Exception e) {
                System.out.println("无效日期: " + answerValue + " -> " + e.getMessage());
            }
        }

        System.out.println("总输入: " + invalidDateAnswers.length);
        System.out.println("有效日期: " + validCount);
        System.out.println("有效率: " + formatPercentage(validCount, invalidDateAnswers.length));

        // 验证只有有效日期被统计
        assert validCount == 1;
        assert dateCountMap.size() == 1;
        assert dateCountMap.containsKey("2025-04-24");

        System.out.println("✓ 无效日期处理正确");
    }

    @Test
    public void testStatTypeValidation() {
        System.out.println("\n=== 统计类型验证测试 ===");

        String[] validStatTypes = {"year", "month", "day"};
        String[] invalidStatTypes = {"week", "quarter", "invalid"};

        // 测试有效的统计类型
        for (String statType : validStatTypes) {
            boolean isValid = "year".equals(statType) || "month".equals(statType) || "day".equals(statType);
            assert isValid : "统计类型应该有效: " + statType;
            System.out.println("✓ 有效统计类型: " + statType);
        }

        // 测试无效的统计类型
        for (String statType : invalidStatTypes) {
            boolean isValid = "year".equals(statType) || "month".equals(statType) || "day".equals(statType);
            assert !isValid : "统计类型应该无效: " + statType;
            System.out.println("✓ 无效统计类型: " + statType);
        }

        System.out.println("✓ 统计类型验证正确");
    }

    @Test
    public void testConstantValues() {
        System.out.println("\n=== 常量值验证 ===");
        System.out.println("DATE_PICKER: " + ITEM_TYPE_DATE_PICKER);

        // 验证常量值
        assert "date-picker".equals(ITEM_TYPE_DATE_PICKER);

        System.out.println("✓ 常量值正确");
    }
}
