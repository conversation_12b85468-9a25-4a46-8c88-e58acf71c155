package com.cec.stat;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 单选/多选统计功能测试
 */
@SpringBootTest
public class UserFormDataServiceSelectTest {

    /**
     * 测试解析答题值的方法
     */
    @Test
    public void testParseAnswerValue() {
        // 测试单选格式
        String singleSelectValue = "选项1";
        List<String> singleResult = parseAnswerValue(singleSelectValue, "radio-group");
        System.out.println("单选结果: " + singleResult);
        assert singleResult.size() == 1;
        assert "选项1".equals(singleResult.get(0));

        // 测试多选格式
        String multiSelectValue = "[选项1, 选项2, 选项3]";
        List<String> multiResult = parseAnswerValue(multiSelectValue, "checkbox-group");
        System.out.println("多选结果: " + multiResult);
        assert multiResult.size() == 3;
        assert multiResult.contains("选项1");
        assert multiResult.contains("选项2");
        assert multiResult.contains("选项3");

        // 测试边界情况
        String emptyValue = "";
        List<String> emptyResult = parseAnswerValue(emptyValue, "radio-group");
        assert emptyResult.isEmpty();

        String singleInBrackets = "[选项1]";
        List<String> singleInBracketsResult = parseAnswerValue(singleInBrackets, "checkbox-group");
        assert singleInBracketsResult.size() == 1;
        assert "选项1".equals(singleInBracketsResult.get(0));
    }

    /**
     * 模拟解析答题值的方法（从实现中复制）
     */
    private List<String> parseAnswerValue(String answerValue, String itemType) {
        if (answerValue == null || answerValue.trim().isEmpty()) {
            return Arrays.asList();
        }

        // 单选类型：直接返回单个选项
        if ("SELECT".equals(itemType) || "radio-group".equals(itemType)) {
            return Arrays.asList(answerValue.trim());
        }

        // 多选类型：解析数组格式 [选项1, 选项2]
        if ("checkbox-group".equals(itemType)) {
            // 移除首尾的方括号
            String cleanValue = answerValue.trim();
            if (cleanValue.startsWith("[") && cleanValue.endsWith("]")) {
                cleanValue = cleanValue.substring(1, cleanValue.length() - 1);
            }

            // 按逗号分割并清理空格
            return Arrays.stream(cleanValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
        }

        // 默认情况：当作单选处理
        return Arrays.asList(answerValue.trim());
    }

    /**
     * 测试统计逻辑示例
     */
    @Test
    public void testStatisticsLogic() {
        // 模拟答题数据
        String[] singleSelectAnswers = {"选项A", "选项B", "选项A", "选项C", "选项A"};
        String[] multiSelectAnswers = {"[选项1, 选项2]", "[选项1]", "[选项2, 选项3]", "[选项1, 选项3]"};

        System.out.println("=== 单选统计示例 ===");
        java.util.Map<String, Long> singleSelectStats = new java.util.HashMap<>();
        for (String answer : singleSelectAnswers) {
            List<String> options = parseAnswerValue(answer, "radio-group");
            for (String option : options) {
                singleSelectStats.merge(option, 1L, Long::sum);
            }
        }
        singleSelectStats.forEach((option, count) ->
            System.out.println(option + ": " + count + " (" + (count * 100.0 / singleSelectAnswers.length) + "%)"));

        System.out.println("\n=== 多选统计示例 ===");
        java.util.Map<String, Long> multiSelectStats = new java.util.HashMap<>();
        for (String answer : multiSelectAnswers) {
            List<String> options = parseAnswerValue(answer, "checkbox-group");
            for (String option : options) {
                multiSelectStats.merge(option, 1L, Long::sum);
            }
        }
        multiSelectStats.forEach((option, count) ->
            System.out.println(option + ": " + count + " (" + (count * 100.0 / multiSelectAnswers.length) + "%)"));
    }
}
