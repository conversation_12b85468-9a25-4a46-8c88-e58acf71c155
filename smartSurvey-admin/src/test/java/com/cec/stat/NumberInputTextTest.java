package com.cec.stat;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * Number-Input作为文本类型处理测试
 */
public class NumberInputTextTest {

    // 模拟常量
    private static final String ITEM_TYPE_TEXT_INPUT = "text-input";
    private static final String ITEM_TYPE_TEXTAREA = "textarea";
    private static final String ITEM_TYPE_NUMBER_INPUT = "number-input";
    
    /**
     * 判断是否为文本类型
     */
    private static boolean isTextType(String itemType) {
        return ITEM_TYPE_TEXT_INPUT.equals(itemType) ||
               ITEM_TYPE_TEXTAREA.equals(itemType) ||
               ITEM_TYPE_NUMBER_INPUT.equals(itemType);
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return String.format("%.1f%%", ratio * 100);
    }

    @Test
    public void testNumberInputAsText() {
        System.out.println("=== Number-Input作为文本类型测试 ===");
        
        // 验证类型判断
        assert isTextType("text-input");
        assert isTextType("textarea");
        assert isTextType("number-input"); // ✅ 新增支持
        assert !isTextType("select");
        assert !isTextType("radio-group");
        
        System.out.println("✓ Number-Input被正确识别为文本类型");
    }

    @Test
    public void testNumberInputStatistics() {
        System.out.println("\n=== Number-Input统计逻辑测试 ===");
        
        // 模拟数字输入框的答题数据
        String[] numberInputAnswers = {
            "25",      // 第1个人输入了25
            "30",      // 第2个人输入了30
            "25",      // 第3个人输入了25（重复值）
            "40",      // 第4个人输入了40
            "35"       // 第5个人输入了35
        };
        
        // 统计填写情况（类似文本统计）
        Map<String, Integer> valueCountMap = new HashMap<>();
        
        for (String answer : numberInputAnswers) {
            valueCountMap.merge(answer, 1, Integer::sum);
        }
        
        long fillCount = numberInputAnswers.length; // 填写该题的总人数 = 5
        long totalAnswerCount = 6; // 假设总共6个人参与问卷
        
        System.out.println("填写该题的总人数: " + fillCount);
        System.out.println("总答题人数: " + totalAnswerCount);
        System.out.println("填写率: " + formatPercentage(fillCount, totalAnswerCount));
        
        System.out.println("\n=== 数字输入内容统计 ===");
        valueCountMap.entrySet().stream()
            .sorted((a, b) -> Integer.compare(Integer.parseInt(b.getKey()), Integer.parseInt(a.getKey())))
            .forEach(entry -> {
                String value = entry.getKey();
                Integer count = entry.getValue();
                System.out.println("数值" + value + ": " + count + "人填写");
            });
        
        // 验证结果
        assert valueCountMap.get("25").equals(2); // 25被填写了2次
        assert valueCountMap.get("30").equals(1); // 30被填写了1次
        assert valueCountMap.get("40").equals(1); // 40被填写了1次
        assert valueCountMap.get("35").equals(1); // 35被填写了1次
        
        System.out.println("✓ Number-Input统计正确！");
    }

    @Test
    public void testMixedTextTypes() {
        System.out.println("\n=== 混合文本类型处理测试 ===");
        
        // 测试不同文本类型的处理
        String[][] testCases = {
            {"text-input", "这是单行文本"},
            {"textarea", "这是多行文本\n第二行内容"},
            {"number-input", "123.45"}, // ✅ 数字输入框
            {"number-input", "0"},
            {"number-input", "-50"}
        };
        
        Map<String, Integer> allAnswers = new HashMap<>();
        
        for (String[] testCase : testCases) {
            String type = testCase[0];
            String value = testCase[1];
            
            System.out.println("类型: " + type + ", 值: " + value + " -> 是否为文本类型: " + isTextType(type));
            
            // 验证所有类型都被正确识别为文本类型
            assert isTextType(type);
            
            // 统计所有答题内容
            allAnswers.merge(value, 1, Integer::sum);
        }
        
        System.out.println("\n=== 所有文本内容统计 ===");
        allAnswers.forEach((value, count) -> {
            System.out.println("内容: \"" + value + "\" -> " + count + "次");
        });
        
        System.out.println("✓ 混合文本类型处理正确！");
    }

    @Test
    public void testNumberInputEdgeCases() {
        System.out.println("\n=== Number-Input边界情况测试 ===");
        
        // 测试各种数字格式
        String[] edgeCases = {
            "0",           // 零
            "-1",          // 负数
            "3.14159",     // 小数
            "1000000",     // 大数
            "0.001",       // 小数
            "",            // 空值
            " 42 ",        // 带空格
            "1.23e10"      // 科学计数法
        };
        
        Map<String, Integer> edgeCaseStats = new HashMap<>();
        int validCount = 0;
        
        for (String value : edgeCases) {
            String trimmedValue = value.trim();
            
            // 模拟实际处理逻辑：过滤空值
            if (!trimmedValue.isEmpty()) {
                edgeCaseStats.merge(trimmedValue, 1, Integer::sum);
                validCount++;
            }
            
            System.out.println("输入: \"" + value + "\" -> 处理后: \"" + trimmedValue + "\" -> " + 
                (!trimmedValue.isEmpty() ? "有效" : "无效"));
        }
        
        System.out.println("\n=== 边界情况统计结果 ===");
        System.out.println("总输入数: " + edgeCases.length);
        System.out.println("有效输入数: " + validCount);
        System.out.println("有效率: " + formatPercentage(validCount, edgeCases.length));
        
        edgeCaseStats.forEach((value, count) -> {
            System.out.println("值: \"" + value + "\" -> " + count + "次");
        });
        
        // 验证空值被正确过滤
        assert !edgeCaseStats.containsKey("");
        assert edgeCaseStats.containsKey("42"); // 空格被正确去除
        
        System.out.println("✓ Number-Input边界情况处理正确！");
    }

    @Test
    public void testConstantValues() {
        System.out.println("\n=== 常量值验证 ===");
        System.out.println("TEXT_INPUT: " + ITEM_TYPE_TEXT_INPUT);
        System.out.println("TEXTAREA: " + ITEM_TYPE_TEXTAREA);
        System.out.println("NUMBER_INPUT: " + ITEM_TYPE_NUMBER_INPUT);
        
        // 验证常量值
        assert "text-input".equals(ITEM_TYPE_TEXT_INPUT);
        assert "textarea".equals(ITEM_TYPE_TEXTAREA);
        assert "number-input".equals(ITEM_TYPE_NUMBER_INPUT);
        
        System.out.println("✓ 常量值正确");
    }
}
