package com.cec.stat;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件统计测试
 */
public class FileStatisticsTest {

    // 模拟常量
    private static final String ITEM_TYPE_UPLOAD = "upload";

    /**
     * 判断是否为文件类型
     */
    private static boolean isFileType(String itemType) {
        return ITEM_TYPE_UPLOAD.equals(itemType);
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return String.format("%.1f%%", ratio * 100);
    }

    @Test
    public void testFileTypeValidation() {
        System.out.println("=== 文件类型验证测试 ===");

        // 测试文件类型识别
        assert isFileType("upload");
        assert !isFileType("text-input");
        assert !isFileType("select");

        System.out.println("✓ 文件类型验证正确");
    }

    @Test
    public void testSpecialFormatFileDataParsing() {
        System.out.println("\n=== 特殊格式文件数据解析测试 ===");

        // 模拟实际的特殊格式文件上传数据
        String specialFormatData = "[{uid=1020, name=支付宝收款码2.png, thumb=, status=success, size=4828, url=blob:http://localhost:5173/3f999762-1f8f-4f88-abd8-4d19b01268de, percent=100, response={\"code\":200,\"msg\":\"操作成功\",\"data\":{\"url\":\"http://***********:10005/smart-survey/2025/07/23/6886b719d12344098b90d26c8a6c96d9.png\",\"fileName\":\"支付宝收款码2.png\",\"ossId\":\"1947866379326414850\"}}}]";

        List<FileItem> fileList = parseSpecialFormatFileData(specialFormatData);

        System.out.println("特殊格式解析结果:");
        for (FileItem file : fileList) {
            System.out.println("文件名: " + file.fileName);
            System.out.println("URL: " + file.url);
            System.out.println("OSS ID: " + file.ossId);
            System.out.println("大小: " + file.size + " bytes");
            System.out.println("---");
        }

        // 验证解析结果
        assert fileList.size() == 1;
        FileItem file = fileList.get(0);
        assert "支付宝收款码2.png".equals(file.fileName);
        assert "http://***********:10005/smart-survey/2025/07/23/6886b719d12344098b90d26c8a6c96d9.png".equals(file.url);
        assert "1947866379326414850".equals(file.ossId);
        assert file.size == 4828L;

        System.out.println("✓ 特殊格式文件数据解析正确");
    }

    @Test
    public void testFileDataParsing() {
        System.out.println("\n=== 标准JSON文件数据解析测试（兼容性）===");

        // 模拟标准JSON格式文件上传数据
        String fileDataJson = "[{\"uid\":1020,\"name\":\"支付宝收款码2.png\",\"thumb\":\"\",\"status\":\"success\",\"size\":4828,\"url\":\"blob:http://localhost:5173/3f999762-1f8f-4f88-abd8-4d19b01268de\",\"percent\":100,\"response\":{\"code\":200,\"msg\":\"操作成功\",\"data\":{\"url\":\"http://***********:10005/smart-survey/2025/07/23/6886b719d12344098b90d26c8a6c96d9.png\",\"fileName\":\"支付宝收款码2.png\",\"ossId\":\"1947866379326414850\"}}}]";

        List<FileItem> fileList = parseFileData(fileDataJson);

        System.out.println("标准JSON解析结果:");
        for (FileItem file : fileList) {
            System.out.println("文件名: " + file.fileName);
            System.out.println("URL: " + file.url);
            System.out.println("OSS ID: " + file.ossId);
            System.out.println("大小: " + file.size + " bytes");
            System.out.println("---");
        }

        // 验证解析结果
        assert fileList.size() == 1;
        FileItem file = fileList.get(0);
        assert "支付宝收款码2.png".equals(file.fileName);
        assert "http://***********:10005/smart-survey/2025/07/23/6886b719d12344098b90d26c8a6c96d9.png".equals(file.url);
        assert "1947866379326414850".equals(file.ossId);
        assert file.size == 4828L;

        System.out.println("✓ 标准JSON文件数据解析正确");
    }

    @Test
    public void testMultipleFiles() {
        System.out.println("\n=== 多文件解析测试 ===");

        // 模拟多个文件上传数据
        String multiFileDataJson = "[" +
            "{\"uid\":1020,\"name\":\"文件1.png\",\"size\":1000,\"response\":{\"data\":{\"url\":\"http://example.com/file1.png\",\"fileName\":\"文件1.png\",\"ossId\":\"123\"}}}," +
            "{\"uid\":1021,\"name\":\"文件2.pdf\",\"size\":2000,\"response\":{\"data\":{\"url\":\"http://example.com/file2.pdf\",\"fileName\":\"文件2.pdf\",\"ossId\":\"456\"}}}" +
            "]";

        List<FileItem> fileList = parseFileData(multiFileDataJson);

        System.out.println("解析到 " + fileList.size() + " 个文件:");
        for (int i = 0; i < fileList.size(); i++) {
            FileItem file = fileList.get(i);
            System.out.println((i + 1) + ". " + file.fileName + " (" + file.size + " bytes)");
        }

        // 验证解析结果
        assert fileList.size() == 2;
        assert "文件1.png".equals(fileList.get(0).fileName);
        assert "文件2.pdf".equals(fileList.get(1).fileName);

        System.out.println("✓ 多文件解析正确");
    }

    @Test
    public void testSizeExtraction() {
        System.out.println("\n=== Size提取测试 ===");

        // 测试从key=value格式中提取size
        String keyValuePart = "uid=1020, name=支付宝收款码2.png, thumb=, status=success, size=4828, url=blob:http://localhost:5173/3f999762-1f8f-4f88-abd8-4d19b01268de, percent=100, ";

        Long size = extractSizeFromKeyValuePart(keyValuePart);

        System.out.println("输入: " + keyValuePart);
        System.out.println("提取的size: " + size);

        // 验证提取结果
        assert size.equals(4828L);

        System.out.println("✓ Size提取正确");
    }

    @Test
    public void testInvalidFileData() {
        System.out.println("\n=== 无效文件数据处理测试 ===");

        String[] invalidDataList = {
            "",                    // 空字符串
            "invalid json",        // 无效JSON
            "[]",                  // 空数组
            "[{\"invalid\": true}]" // 缺少必要字段
        };

        for (String invalidData : invalidDataList) {
            List<FileItem> fileList = parseFileData(invalidData);
            System.out.println("输入: \"" + invalidData + "\" -> 解析结果: " + fileList.size() + " 个文件");

            // 无效数据应该返回空列表
            assert fileList.isEmpty() : "无效数据应该返回空列表";
        }

        System.out.println("✓ 无效文件数据处理正确");
    }

    @Test
    public void testFileStatistics() {
        System.out.println("\n=== 文件统计逻辑测试 ===");

        // 模拟统计场景
        int totalAnswerCount = 10;  // 总答题人数
        int fillCount = 7;          // 填写文件的人数
        int totalFileCount = 12;    // 总文件数量

        String fillRate = formatPercentage(fillCount, totalAnswerCount);

        System.out.println("统计结果:");
        System.out.println("总答题人数: " + totalAnswerCount);
        System.out.println("填写文件人数: " + fillCount);
        System.out.println("填写率: " + fillRate);
        System.out.println("总文件数量: " + totalFileCount);
        System.out.println("平均每人上传文件数: " + String.format("%.1f", (double) totalFileCount / fillCount));

        // 验证统计结果
        assert "70.0%".equals(fillRate);

        System.out.println("✓ 文件统计逻辑正确");
    }

    @Test
    public void testConstantValues() {
        System.out.println("\n=== 常量值验证 ===");
        System.out.println("UPLOAD: " + ITEM_TYPE_UPLOAD);

        // 验证常量值
        assert "upload".equals(ITEM_TYPE_UPLOAD);

        System.out.println("✓ 常量值正确");
    }

    /**
     * 解析特殊格式文件数据（模拟实现）
     */
    private List<FileItem> parseSpecialFormatFileData(String answerValue) {
        List<FileItem> fileList = new ArrayList<>();

        if (answerValue == null || answerValue.trim().isEmpty()) {
            return fileList;
        }

        try {
            // 移除首尾的方括号
            String content = answerValue.trim();
            if (content.startsWith("[") && content.endsWith("}]")) {
                content = content.substring(1, content.length() - 1);
            }

            // 查找response字段的位置
            int responseIndex = content.indexOf("response=");
            if (responseIndex == -1) {
                return fileList;
            }

            // 提取response部分的JSON字符串
            String responseJson = content.substring(responseIndex + 9); // "response=".length() = 9

            // 解析response JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseNode = objectMapper.readTree(responseJson);

            if (responseNode.has("data")) {
                JsonNode dataNode = responseNode.get("data");

                String url = dataNode.has("url") ? dataNode.get("url").asText() : "";
                String fileName = dataNode.has("fileName") ? dataNode.get("fileName").asText() : "";
                String ossId = dataNode.has("ossId") ? dataNode.get("ossId").asText() : "";

                // 从前面的key=value部分提取size
                Long size = extractSizeFromKeyValuePart(content.substring(0, responseIndex));

                FileItem fileItem = new FileItem();
                fileItem.fileName = fileName;
                fileItem.url = url;
                fileItem.ossId = ossId;
                fileItem.size = size;

                fileList.add(fileItem);
            }
        } catch (Exception e) {
            System.out.println("解析特殊格式文件数据失败: " + e.getMessage());
        }

        return fileList;
    }

    /**
     * 从key=value格式的字符串中提取size值
     */
    private Long extractSizeFromKeyValuePart(String keyValuePart) {
        try {
            // 查找size=xxx的模式
            String[] pairs = keyValuePart.split(",\\s*");
            for (String pair : pairs) {
                String trimmedPair = pair.trim();
                if (trimmedPair.startsWith("size=")) {
                    String sizeStr = trimmedPair.substring(5); // "size=".length() = 5
                    return Long.parseLong(sizeStr);
                }
            }
        } catch (Exception e) {
            System.out.println("提取size失败: " + keyValuePart + " -> " + e.getMessage());
        }
        return 0L;
    }

    /**
     * 解析文件数据（模拟实现）
     */
    private List<FileItem> parseFileData(String answerValue) {
        List<FileItem> fileList = new ArrayList<>();

        if (answerValue == null || answerValue.trim().isEmpty()) {
            return fileList;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonArray = objectMapper.readTree(answerValue);

            if (jsonArray.isArray()) {
                for (JsonNode fileNode : jsonArray) {
                    // 解析response中的data字段
                    JsonNode responseNode = fileNode.get("response");
                    if (responseNode != null && responseNode.get("data") != null) {
                        JsonNode dataNode = responseNode.get("data");

                        String url = dataNode.has("url") ? dataNode.get("url").asText() : "";
                        String fileName = dataNode.has("fileName") ? dataNode.get("fileName").asText() : "";
                        String ossId = dataNode.has("ossId") ? dataNode.get("ossId").asText() : "";

                        // 获取文件大小（从外层节点）
                        Long size = fileNode.has("size") ? fileNode.get("size").asLong() : 0L;

                        FileItem fileItem = new FileItem();
                        fileItem.fileName = fileName;
                        fileItem.url = url;
                        fileItem.ossId = ossId;
                        fileItem.size = size;

                        fileList.add(fileItem);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("解析文件数据失败: " + e.getMessage());
        }

        return fileList;
    }

    /**
     * 文件项数据结构
     */
    private static class FileItem {
        String fileName;
        String url;
        String ossId;
        Long size;
    }
}
