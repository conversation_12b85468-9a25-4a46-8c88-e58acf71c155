package com.cec.stat;

import org.junit.jupiter.api.Test;

/**
 * 类型验证测试
 */
public class TypeValidationTest {

    // 模拟常量
    private static final String ITEM_TYPE_SELECT = "select";
    private static final String ITEM_TYPE_RADIO_GROUP = "radio-group";
    private static final String ITEM_TYPE_CHECKBOX_GROUP = "checkbox-group";
    private static final String ITEM_TYPE_NSP = "nsp";
    private static final String ITEM_TYPE_RATING = "rating";
    private static final String ITEM_TYPE_RATE = "rate";
    
    private static final String ITEM_TYPE_TEXT_INPUT = "text-input";
    private static final String ITEM_TYPE_TEXTAREA = "textarea";
    private static final String ITEM_TYPE_NUMBER_INPUT = "number-input";
    
    /**
     * 判断是否为支持的选择类型表单项
     */
    private static boolean isSupportedSelectType(String itemType) {
        return ITEM_TYPE_SELECT.equals(itemType) || 
               ITEM_TYPE_RADIO_GROUP.equals(itemType) || 
               ITEM_TYPE_CHECKBOX_GROUP.equals(itemType) ||
               ITEM_TYPE_NSP.equals(itemType) ||
               ITEM_TYPE_RATING.equals(itemType) ||
               ITEM_TYPE_RATE.equals(itemType);
    }
    
    /**
     * 判断是否为文本类型
     */
    private static boolean isTextType(String itemType) {
        return ITEM_TYPE_TEXT_INPUT.equals(itemType) ||
               ITEM_TYPE_TEXTAREA.equals(itemType) ||
               ITEM_TYPE_NUMBER_INPUT.equals(itemType);
    }

    /**
     * 模拟文本统计方法的类型验证
     */
    private void validateTextTypeForStatistics(String itemType) {
        if (!isTextType(itemType)) {
            throw new RuntimeException("该表单项不是文本类型，当前类型：" + itemType);
        }
    }

    /**
     * 模拟选择统计方法的类型验证
     */
    private void validateSelectTypeForStatistics(String itemType) {
        if (!isSupportedSelectType(itemType)) {
            throw new RuntimeException("该表单项不是单选或多选类型，当前类型：" + itemType);
        }
    }

    @Test
    public void testTextTypeValidation() {
        System.out.println("=== 文本类型验证测试 ===");
        
        // 测试有效的文本类型
        String[] validTextTypes = {"text-input", "textarea", "number-input"};
        
        for (String type : validTextTypes) {
            try {
                validateTextTypeForStatistics(type);
                System.out.println("✓ " + type + " -> 验证通过（文本类型）");
            } catch (Exception e) {
                System.out.println("✗ " + type + " -> 验证失败：" + e.getMessage());
                assert false : "文本类型验证失败";
            }
        }
        
        // 测试无效的类型（选择类型）
        String[] invalidTypes = {"select", "radio-group", "checkbox-group", "nsp", "rating", "rate"};
        
        for (String type : invalidTypes) {
            try {
                validateTextTypeForStatistics(type);
                System.out.println("✗ " + type + " -> 应该验证失败但通过了");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                System.out.println("✓ " + type + " -> 正确拒绝：" + e.getMessage());
            }
        }
        
        System.out.println("文本类型验证测试通过！");
    }

    @Test
    public void testSelectTypeValidation() {
        System.out.println("\n=== 选择类型验证测试 ===");
        
        // 测试有效的选择类型
        String[] validSelectTypes = {"select", "radio-group", "checkbox-group", "nsp", "rating", "rate"};
        
        for (String type : validSelectTypes) {
            try {
                validateSelectTypeForStatistics(type);
                System.out.println("✓ " + type + " -> 验证通过（选择类型）");
            } catch (Exception e) {
                System.out.println("✗ " + type + " -> 验证失败：" + e.getMessage());
                assert false : "选择类型验证失败";
            }
        }
        
        // 测试无效的类型（文本类型）
        String[] invalidTypes = {"text-input", "textarea", "number-input"};
        
        for (String type : invalidTypes) {
            try {
                validateSelectTypeForStatistics(type);
                System.out.println("✗ " + type + " -> 应该验证失败但通过了");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                System.out.println("✓ " + type + " -> 正确拒绝：" + e.getMessage());
            }
        }
        
        System.out.println("选择类型验证测试通过！");
    }

    @Test
    public void testUnsupportedTypes() {
        System.out.println("\n=== 不支持类型测试 ===");
        
        // 测试完全不支持的类型
        String[] unsupportedTypes = {"image", "file-upload", "date", "time", "unknown-type"};
        
        for (String type : unsupportedTypes) {
            // 测试文本统计验证
            try {
                validateTextTypeForStatistics(type);
                System.out.println("✗ " + type + " -> 文本统计应该拒绝但通过了");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                System.out.println("✓ " + type + " -> 文本统计正确拒绝：" + e.getMessage());
            }
            
            // 测试选择统计验证
            try {
                validateSelectTypeForStatistics(type);
                System.out.println("✗ " + type + " -> 选择统计应该拒绝但通过了");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                System.out.println("✓ " + type + " -> 选择统计正确拒绝：" + e.getMessage());
            }
        }
        
        System.out.println("不支持类型测试通过！");
    }

    @Test
    public void testTypeClassification() {
        System.out.println("\n=== 类型分类测试 ===");
        
        // 验证类型分类的互斥性
        String[] allTypes = {
            "text-input", "textarea", "number-input",  // 文本类型
            "select", "radio-group", "checkbox-group", "nsp", "rating", "rate"  // 选择类型
        };
        
        for (String type : allTypes) {
            boolean isText = isTextType(type);
            boolean isSelect = isSupportedSelectType(type);
            
            System.out.println(type + " -> 文本类型: " + isText + ", 选择类型: " + isSelect);
            
            // 验证类型分类的互斥性（一个类型不能同时是文本和选择类型）
            assert !(isText && isSelect) : "类型不能同时是文本和选择类型: " + type;
            
            // 验证每个类型至少属于一种分类
            assert (isText || isSelect) : "类型必须属于文本或选择类型之一: " + type;
        }
        
        System.out.println("✓ 所有类型都正确分类且互斥");
        System.out.println("类型分类测试通过！");
    }

    @Test
    public void testErrorMessages() {
        System.out.println("\n=== 错误消息测试 ===");
        
        // 测试错误消息是否包含类型信息
        try {
            validateTextTypeForStatistics("select");
            assert false : "应该抛出异常";
        } catch (Exception e) {
            String message = e.getMessage();
            assert message.contains("select") : "错误消息应该包含类型信息";
            assert message.contains("不是文本类型") : "错误消息应该说明期望的类型";
            System.out.println("✓ 文本统计错误消息正确：" + message);
        }
        
        try {
            validateSelectTypeForStatistics("text-input");
            assert false : "应该抛出异常";
        } catch (Exception e) {
            String message = e.getMessage();
            assert message.contains("text-input") : "错误消息应该包含类型信息";
            assert message.contains("不是单选或多选类型") : "错误消息应该说明期望的类型";
            System.out.println("✓ 选择统计错误消息正确：" + message);
        }
        
        System.out.println("错误消息测试通过！");
    }
}
