package com.cec.test;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多选逻辑测试
 */
public class MultiSelectLogicTest {

    /**
     * 模拟解析答题值的方法
     */
    private List<String> parseAnswerValue(String answerValue, String itemType) {
        if (answerValue == null || answerValue.trim().isEmpty()) {
            return Arrays.asList();
        }

        // 单选类型：直接返回单个选项
        if ("select".equals(itemType) || "radio-group".equals(itemType)) {
            return Arrays.asList(answerValue.trim());
        }

        // 多选类型：解析数组格式 [选项1, 选项2]
        if ("checkbox-group".equals(itemType)) {
            // 移除首尾的方括号
            String cleanValue = answerValue.trim();
            if (cleanValue.startsWith("[") && cleanValue.endsWith("]")) {
                cleanValue = cleanValue.substring(1, cleanValue.length() - 1);
            }

            // 按逗号分割并清理空格
            return Arrays.stream(cleanValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
        }

        // 默认情况：当作单选处理
        return Arrays.asList(answerValue.trim());
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(long numerator, long denominator) {
        if (denominator == 0) {
            return "0.0%";
        }
        double ratio = (double) numerator / denominator;
        return String.format("%.1f%%", ratio * 100);
    }

    @Test
    public void testMultiSelectLogic() {
        System.out.println("=== 多选逻辑测试 ===");

        // 模拟答题数据：3个人填写了多选题
        String[] multiSelectAnswers = {
            "[选项1, 选项3]",  // 第1个人选择了选项1和3
            "[选项1, 选项2]",  // 第2个人选择了选项1和2
            "[选项2, 选项3]"   // 第3个人选择了选项2和3
        };

        // 统计每个选项被选择的人数
        Map<String, Long> optionCountMap = new HashMap<>();

        for (String answer : multiSelectAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "checkbox-group");
            System.out.println("答题: " + answer + " -> 解析结果: " + selectedOptions);

            // 对于每个人的答题，统计他选择了哪些选项（每个选项每人只计数一次）
            for (String option : selectedOptions) {
                optionCountMap.merge(option, 1L, Long::sum);
            }
        }

        long fillCount = multiSelectAnswers.length; // 填写该题的总人数 = 3

        System.out.println("\n=== 统计结果 ===");
        System.out.println("填写该题的总人数: " + fillCount);

        optionCountMap.forEach((option, count) -> {
            String fillRate = formatPercentage(count, fillCount);
            System.out.println(option + ": " + count + "人选择 (" + fillRate + ")");
        });

        // 验证结果
        System.out.println("\n=== 验证结果 ===");

        // 选项1：被第1、2个人选择，应该是2人，填写率66.7%
        assert optionCountMap.get("选项1").equals(2L);
        System.out.println("✓ 选项1: 2人选择，填写率66.7%");

        // 选项2：被第2、3个人选择，应该是2人，填写率66.7%
        assert optionCountMap.get("选项2").equals(2L);
        System.out.println("✓ 选项2: 2人选择，填写率66.7%");

        // 选项3：被第1、3个人选择，应该是2人，填写率66.7%
        assert optionCountMap.get("选项3").equals(2L);
        System.out.println("✓ 选项3: 2人选择，填写率66.7%");

        System.out.println("\n所有测试通过！多选逻辑正确！");
    }

    @Test
    public void testSingleSelectLogic() {
        System.out.println("=== 单选逻辑测试 ===");

        // 模拟答题数据：3个人填写了单选题
        String[] singleSelectAnswers = {
            "选项A",  // 第1个人选择了选项A
            "选项B",  // 第2个人选择了选项B
            "选项A"   // 第3个人选择了选项A
        };

        // 统计每个选项被选择的人数
        Map<String, Long> optionCountMap = new HashMap<>();

        for (String answer : singleSelectAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "radio-group");

            for (String option : selectedOptions) {
                optionCountMap.merge(option, 1L, Long::sum);
            }
        }

        long fillCount = singleSelectAnswers.length; // 填写该题的总人数 = 3

        System.out.println("填写该题的总人数: " + fillCount);

        optionCountMap.forEach((option, count) -> {
            String fillRate = formatPercentage(count, fillCount);
            System.out.println(option + ": " + count + "人选择 (" + fillRate + ")");
        });

        // 验证结果
        assert optionCountMap.get("选项A").equals(2L); // 2人选择，66.7%
        assert optionCountMap.get("选项B").equals(1L); // 1人选择，33.3%

        System.out.println("✓ 单选逻辑正确！");
    }

    @Test
    public void testEdgeCase() {
        System.out.println("=== 边界情况测试 ===");

        // 测试只有一个人填写多选题的情况
        String[] onePersonAnswers = {"[选项1, 选项3]"};

        Map<String, Long> optionCountMap = new HashMap<>();

        for (String answer : onePersonAnswers) {
            List<String> selectedOptions = parseAnswerValue(answer, "checkbox-group");
            for (String option : selectedOptions) {
                optionCountMap.merge(option, 1L, Long::sum);
            }
        }

        long fillCount = onePersonAnswers.length; // 填写该题的总人数 = 1

        System.out.println("只有1个人填写，他选择了[选项1, 选项3]");

        optionCountMap.forEach((option, count) -> {
            String fillRate = formatPercentage(count, fillCount);
            System.out.println(option + ": " + count + "人选择 (" + fillRate + ")");
        });

        // 验证：选项1和选项3都应该是100%的填写率
        assert optionCountMap.get("选项1").equals(1L);
        assert optionCountMap.get("选项3").equals(1L);
        assert formatPercentage(1, 1).equals("100.0%");

        System.out.println("✓ 边界情况正确：选项1和选项3都是100%填写率");
    }
}
